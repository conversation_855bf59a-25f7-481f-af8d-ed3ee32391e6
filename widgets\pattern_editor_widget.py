#!/usr/bin/env python3
"""
Reusable Pattern Editor Dialog for Adventure Chess
Used by both piece editor and ability editor for movement/attack patterns
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QPushButton, QLabel, QCheckBox)
from PyQt6.QtCore import Qt

# Import shared UI utilities
from ui.ui_shared_components import create_legend_item, create_dialog_buttons, create_grid_instructions

class PatternEditorDialog(QDialog):
    """
    Reusable pattern editor dialog that supports both single pattern and dual pattern modes
    """

    # Class-level state storage for checkbox persistence
    _last_checkbox_state = {
        'starting_square_checked': False,
        'continue_off_board_checked': False
    }

    def __init__(self, movement_pattern=None, attack_pattern=None, title="Pattern Editor", parent=None, checkbox_states=None, highlighted_preset=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setMinimumSize(400, 500)

        # Determine if we're in dual pattern mode (buff/debuff) or single pattern mode (piece)
        self.dual_mode = attack_pattern is not None
        self.highlighted_preset = highlighted_preset  # Preset to highlight when opened

        # For dual mode, we use a single pattern with 3-color system like piece creator
        # 0 = empty, 1 = move only (blue), 2 = capture only (red), 3 = move and capture (purple)
        if self.dual_mode:
            # Convert dual patterns to single 3-color pattern if provided
            if movement_pattern is not None and attack_pattern is not None:
                self.pattern = self.convert_dual_to_single(movement_pattern, attack_pattern)
            else:
                self.pattern = [[0 for _ in range(8)] for _ in range(8)]
        else:
            # Single mode uses the pattern directly
            if movement_pattern is None:
                self.pattern = [[0 for _ in range(8)] for _ in range(8)]
            else:
                self.pattern = [row[:] for row in movement_pattern]  # Deep copy

        # Piece position
        self.piece_pos = [3, 3]  # Start in center

        # Initialize checkbox states from parameter or defaults
        if checkbox_states is not None:
            self.include_starting_square = checkbox_states.get('starting_square_checked', False)
            self.continue_off_board = checkbox_states.get('continue_off_board_checked', False)
        else:
            self.include_starting_square = False
            self.continue_off_board = False

        self.setup_ui()
        self.update_visual()
    
    def convert_dual_to_single(self, movement_pattern, attack_pattern):
        """Convert separate movement and attack patterns to single 3-color pattern"""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        
        for r in range(8):
            for c in range(8):
                move_enabled = movement_pattern[r][c] > 0 if isinstance(movement_pattern[r][c], int) else bool(movement_pattern[r][c])
                attack_enabled = attack_pattern[r][c] > 0 if isinstance(attack_pattern[r][c], int) else bool(attack_pattern[r][c])
                
                if move_enabled and attack_enabled:
                    pattern[r][c] = 3  # Both
                elif move_enabled:
                    pattern[r][c] = 1  # Move only
                elif attack_enabled:
                    pattern[r][c] = 2  # Attack only
                else:
                    pattern[r][c] = 0  # Empty
        
        return pattern
    
    def convert_single_to_dual(self):
        """Convert single 3-color pattern to separate movement and attack patterns"""
        movement_pattern = [[0 for _ in range(8)] for _ in range(8)]
        attack_pattern = [[0 for _ in range(8)] for _ in range(8)]
        
        for r in range(8):
            for c in range(8):
                state = self.pattern[r][c]
                if state == 1 or state == 3:  # Move only or both
                    movement_pattern[r][c] = 1
                if state == 2 or state == 3:  # Attack only or both
                    attack_pattern[r][c] = 1
        
        return movement_pattern, attack_pattern
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()
        
        # Title description
        if self.dual_mode:
            title_label = QLabel("Click tiles to cycle: Empty → Move → Attack → Both → Action → Any")
            title_label.setStyleSheet("font-weight: bold; padding: 5px; color: #333;")
            layout.addWidget(title_label)
        else:
            title_label = QLabel("Click tiles to cycle: Empty → Move → Attack → Both → Action → Any")
            title_label.setStyleSheet("font-weight: bold; padding: 5px; color: #333;")
            layout.addWidget(title_label)

        # Checkboxes and main buttons in one row
        checkbox_button_layout = QHBoxLayout()

        self.starting_square_check = QCheckBox("Include Starting Square")
        self.starting_square_check.setToolTip("Allow targeting the piece's starting position")
        self.starting_square_check.stateChanged.connect(self.on_starting_square_changed)
        checkbox_button_layout.addWidget(self.starting_square_check)

        self.continue_off_board_check = QCheckBox("Continue Off Board")
        self.continue_off_board_check.setToolTip("continues board pattern off edges of map")
        self.continue_off_board_check.stateChanged.connect(self.on_continue_off_board_changed)
        checkbox_button_layout.addWidget(self.continue_off_board_check)

        checkbox_button_layout.addStretch()

        # Add main action buttons to the same row
        save_btn, cancel_btn = create_dialog_buttons("Save Pattern", "Cancel")
        cancel_btn.clicked.connect(self.reject)
        save_btn.clicked.connect(self.accept)

        checkbox_button_layout.addWidget(cancel_btn)
        checkbox_button_layout.addWidget(save_btn)

        layout.addLayout(checkbox_button_layout)

        # Set checkbox states from initialization
        self.starting_square_check.setChecked(self.include_starting_square)
        self.continue_off_board_check.setChecked(self.continue_off_board)

        # Quick Patterns
        pattern_layout = QHBoxLayout()
        pattern_layout.addWidget(QLabel("Quick Patterns:"))
        
        # Chess piece pattern buttons with styling
        pattern_buttons = [
            ("♜", "rook", "Orthogonal lines (like Rook)"),
            ("♝", "bishop", "Diagonal lines (like Bishop)"),
            ("♛", "queen", "All directions (like Queen)"),
            ("♞", "knight", "L-shaped moves (like Knight)"),
            ("♚", "king", "Adjacent squares (like King)"),
            ("🌐", "global", "Entire board range")
        ]

        # Track pattern buttons for highlighting
        self.pattern_preset_buttons = []
        self.current_preset_type = self.highlighted_preset  # Initialize with highlighted preset
        
        for symbol, preset_type, tooltip in pattern_buttons:
            btn = QPushButton(symbol)
            btn.setFixedSize(35, 30)
            btn.setToolTip(f"{tooltip}")

            # Store button reference with preset type
            self.pattern_preset_buttons.append((btn, preset_type))

            # Set initial styling based on highlighted preset
            self.update_preset_button_style(btn, preset_type)

            btn.clicked.connect(lambda checked, pt=preset_type: self.select_pattern_preset(pt))
            pattern_layout.addWidget(btn)
        
        pattern_layout.addStretch()

        clear_preset_btn = QPushButton("Clear")
        clear_preset_btn.clicked.connect(self.clear_pattern)
        clear_preset_btn.setToolTip("Clear all tiles on the board")
        pattern_layout.addWidget(clear_preset_btn)
        
        layout.addLayout(pattern_layout)
        
        # Grid
        self.grid = []
        grid_layout = QGridLayout()
        
        for row in range(8):
            row_btns = []
            for col in range(8):
                btn = QPushButton()
                btn.setFixedSize(35, 35)
                btn.setCheckable(True)
                btn.setStyleSheet("background: #eee;")
                btn.clicked.connect(lambda checked, r=row, c=col: self.toggle_tile(r, c))
                btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                btn.customContextMenuRequested.connect(lambda pos, r=row, c=col: self.move_piece(r, c))
                grid_layout.addWidget(btn, row, col)
                row_btns.append(btn)
            self.grid.append(row_btns)
        
        layout.addLayout(grid_layout)
        
        # Legend using shared utilities with clickable color selection
        legend_layout = QVBoxLayout()
        legend_label = QLabel("Legend (click colors to select painting mode):")
        legend_label.setStyleSheet("font-weight: bold;")
        legend_layout.addWidget(legend_label)

        # Current painting mode
        self.current_paint_mode = None  # None means normal cycling mode

        # First row of legend
        legend_row1 = QHBoxLayout()

        # Clear - Empty
        self.clear_btn, clear_label = create_legend_item("#eee", "black", "Clear Tile")
        self.clear_btn.setEnabled(True)  # Make clickable
        self.clear_btn.clicked.connect(lambda: self.set_paint_mode(0))
        self.clear_btn.setToolTip("Click to select clear mode, then click tiles to clear them")
        legend_row1.addWidget(self.clear_btn)
        legend_row1.addWidget(clear_label)

        # Blue - Move only
        self.blue_btn, blue_label = create_legend_item("#4444ff", "black", "Move Only")
        self.blue_btn.setEnabled(True)  # Make clickable
        self.blue_btn.clicked.connect(lambda: self.set_paint_mode(1))
        self.blue_btn.setToolTip("Click to select move mode, then click tiles to mark as move-only")
        legend_row1.addWidget(self.blue_btn)
        legend_row1.addWidget(blue_label)

        # Red - Attack only
        self.red_btn, red_label = create_legend_item("#ff4444", "black", "Attack Only")
        self.red_btn.setEnabled(True)  # Make clickable
        self.red_btn.clicked.connect(lambda: self.set_paint_mode(2))
        self.red_btn.setToolTip("Click to select attack mode, then click tiles to mark as attack-only")
        legend_row1.addWidget(self.red_btn)
        legend_row1.addWidget(red_label)

        legend_row1.addStretch()
        legend_layout.addLayout(legend_row1)

        # Second row of legend
        legend_row2 = QHBoxLayout()

        # Purple - Move and attack
        self.purple_btn, purple_label = create_legend_item("#aa44aa", "black", "Move & Attack")
        self.purple_btn.setEnabled(True)  # Make clickable
        self.purple_btn.clicked.connect(lambda: self.set_paint_mode(3))
        self.purple_btn.setToolTip("Click to select both mode, then click tiles to mark as move and attack")
        legend_row2.addWidget(self.purple_btn)
        legend_row2.addWidget(purple_label)

        # Yellow - Action
        self.yellow_btn, yellow_label = create_legend_item("#ffff44", "black", "Action")
        self.yellow_btn.setEnabled(True)  # Make clickable
        self.yellow_btn.clicked.connect(lambda: self.set_paint_mode(4))
        self.yellow_btn.setToolTip("Click to select action mode, then click tiles to mark as action")
        legend_row2.addWidget(self.yellow_btn)
        legend_row2.addWidget(yellow_label)

        # Green - Any
        self.green_btn, green_label = create_legend_item("#66ff66", "black", "Any")
        self.green_btn.setEnabled(True)  # Make clickable
        self.green_btn.clicked.connect(lambda: self.set_paint_mode(5))
        self.green_btn.setToolTip("Click to select any mode, then click tiles to mark as any action")
        legend_row2.addWidget(self.green_btn)
        legend_row2.addWidget(green_label)

        legend_row2.addStretch()
        legend_layout.addLayout(legend_row2)

        # Mode indicator
        self.mode_label = QLabel("Mode: Normal (click tiles to cycle colors)")
        self.mode_label.setStyleSheet("font-style: italic; color: #666; padding: 5px;")
        legend_layout.addWidget(self.mode_label)

        layout.addLayout(legend_layout)
        
        # Instructions using shared utility
        instructions = create_grid_instructions("Click legend colors to select paint mode, or use normal cycling mode. Right-click to move piece position.")
        layout.addWidget(instructions)

        # Reset mode button (consolidated layout)
        reset_mode_layout = QHBoxLayout()
        reset_mode_btn = QPushButton("Normal Mode")
        reset_mode_btn.clicked.connect(lambda: self.set_paint_mode(None))
        reset_mode_btn.setToolTip("Return to normal cycling mode")
        reset_mode_layout.addWidget(reset_mode_btn)
        reset_mode_layout.addStretch()
        layout.addLayout(reset_mode_layout)
        self.setLayout(layout)
    
    def move_piece(self, row, col):
        """Move the piece to a new position"""
        self.piece_pos = [row, col]
        self.update_visual()

    def on_starting_square_changed(self, state):
        """Handle starting square checkbox change"""
        self.include_starting_square = state == Qt.CheckState.Checked.value

    def on_continue_off_board_changed(self, state):
        """Handle continue off board checkbox change"""
        self.continue_off_board = state == Qt.CheckState.Checked.value

    def save_checkbox_states(self):
        """Save current checkbox states for persistence (both class-level and JSON)"""
        # Update class-level state for immediate use
        PatternEditorDialog._last_checkbox_state = {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }

    def restore_checkbox_states(self):
        """Restore checkbox states from saved state"""
        state = PatternEditorDialog._last_checkbox_state
        self.starting_square_check.setChecked(state.get('starting_square_checked', False))
        self.continue_off_board_check.setChecked(state.get('continue_off_board_checked', False))

    def reset_to_defaults(self):
        """Reset checkbox states to default values"""
        self.starting_square_check.setChecked(False)
        self.continue_off_board_check.setChecked(False)
        # Update class-level state to reflect defaults
        PatternEditorDialog._last_checkbox_state = {
            'starting_square_checked': False,
            'continue_off_board_checked': False
        }

    def accept(self):
        """Override accept to save checkbox states before closing"""
        self.save_checkbox_states()
        super().accept()
    
    def set_paint_mode(self, mode):
        """Set the current paint mode for direct color selection"""
        self.current_paint_mode = mode

        # Update legend button styles to show selection
        legend_buttons = [self.clear_btn, self.blue_btn, self.red_btn, self.purple_btn, self.yellow_btn, self.green_btn]
        mode_names = ["Clear", "Move Only", "Attack Only", "Move & Attack", "Action", "Any"]

        for i, btn in enumerate(legend_buttons):
            if i == mode:
                btn.setStyleSheet(btn.styleSheet() + "; border: 3px solid #000; font-weight: bold;")
                self.mode_label.setText(f"Mode: {mode_names[i]} (click tiles to paint this color)")
            else:
                # Reset to normal style
                original_style = btn.styleSheet().split(';')[0] + ";"  # Keep only background color
                btn.setStyleSheet(original_style)

        if mode is None:
            self.mode_label.setText("Mode: Normal (click tiles to cycle colors)")

    def toggle_tile(self, row, col):
        """Toggle tile state using 5-color system or paint mode"""
        if [row, col] == self.piece_pos:
            return  # Can't toggle piece position

        if self.current_paint_mode is not None:
            # Paint mode: set tile to specific color
            self.pattern[row][col] = self.current_paint_mode
        else:
            # Normal mode: cycle through 6 states (0=empty, 1=move, 2=attack, 3=both, 4=action, 5=any)
            state = self.pattern[row][col]
            self.pattern[row][col] = (state + 1) % 6

        self.update_visual()
    
    def update_visual(self):
        """Update the visual display of the grid"""
        for r in range(8):
            for c in range(8):
                btn = self.grid[r][c]
                
                if [r, c] == self.piece_pos:
                    # Piece position
                    btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold;")
                    btn.setText("♔")
                else:
                    btn.setText("")
                    state = self.pattern[r][c]

                    # 5-color system for all modes (dark theme)
                    if state == 0:
                        btn.setStyleSheet("background: #2d3748; border: 1px solid #4a5568;")  # Empty
                    elif state == 1:
                        btn.setStyleSheet("background: #4444ff; border: 1px solid #0000aa;")  # Move only
                    elif state == 2:
                        btn.setStyleSheet("background: #ff4444; border: 1px solid #aa0000;")  # Attack only
                    elif state == 3:
                        btn.setStyleSheet("background: #aa44aa; border: 1px solid #660066;")  # Move and attack
                    elif state == 4:
                        btn.setStyleSheet("background: #ffff44; border: 1px solid #cccc00;")  # Action (Yellow)
                    else:  # state == 5
                        btn.setStyleSheet("background: #66ff66; border: 1px solid #44cc44;")  # Any
    
    def clear_pattern(self):
        """Clear the current pattern"""
        self.pattern = [[0 for _ in range(8)] for _ in range(8)]
        # Clear preset selection when manually clearing
        self.current_preset_type = None
        self.update_all_preset_button_styles()
        self.update_visual()

    def update_preset_button_style(self, btn, preset_type):
        """Update the style of a single preset button based on selection state"""
        if self.current_preset_type == preset_type:
            # Highlighted style for the current preset
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 3px solid #66aaff;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4488cc, stop:1 #3366aa);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #5599dd, stop:1 #4477bb);
                    border-color: #77bbff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #2255aa, stop:1 #1144aa);
                    border-color: #4488cc;
                }
            """)
        else:
            # Normal style
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #3a3a3a, stop:1 #2a2a2a);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a4a4a, stop:1 #3a3a3a);
                    border-color: #66aaff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #1a1a1a, stop:1 #2a2a2a);
                    border-color: #4488cc;
                }
            """)

    def update_all_preset_button_styles(self):
        """Update all preset button styles based on current selection"""
        for btn, preset_type in self.pattern_preset_buttons:
            self.update_preset_button_style(btn, preset_type)

    def select_pattern_preset(self, preset_type):
        """Select a pattern preset and update button highlighting"""
        # Update current selection
        self.current_preset_type = preset_type

        # Update all button styles
        self.update_all_preset_button_styles()

        # Apply the preset pattern
        self.apply_pattern_preset(preset_type)
    
    def apply_pattern_preset(self, preset_type):
        """Apply a preset pattern based on chess piece movement with auto continue off board"""
        # Clear current pattern
        self.pattern = [[0 for _ in range(8)] for _ in range(8)]

        piece_r, piece_c = self.piece_pos

        # Auto-set continue off board based on preset type
        # King and Knight should NOT continue off board, all others should
        should_continue_off_board = preset_type not in ['king', 'knight']
        self.continue_off_board_check.setChecked(should_continue_off_board)
        self.continue_off_board = should_continue_off_board
        
        if preset_type == "rook":
            # Rook: All squares in orthogonal lines
            # Horizontal line
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = 3  # Move and attack
            # Vertical line
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = 3  # Move and attack
                    
        elif preset_type == "bishop":
            # Bishop: All squares in diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = 3  # Move and attack
                            
        elif preset_type == "queen":
            # Queen: Combination of rook and bishop
            # Orthogonal lines
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = 3
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = 3
            # Diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = 3
                            
        elif preset_type == "knight":
            # Knight: L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2), 
                           (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.pattern[r][c] = 3  # Move and attack
                    
        elif preset_type == "king":
            # King: Adjacent squares (8 directions)
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue  # Skip piece position
                    r, c = piece_r + dr, piece_c + dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        self.pattern[r][c] = 3  # Move and attack
                        
        elif preset_type == "global":
            # Global: Entire board except piece position
            for r in range(8):
                for c in range(8):
                    if [r, c] != self.piece_pos:
                        self.pattern[r][c] = 3  # Move and attack
        
        self.update_visual()
    
    def get_movement_pattern(self):
        """Get the movement pattern"""
        if self.dual_mode:
            movement_pattern, _ = self.convert_single_to_dual()
            return movement_pattern
        else:
            return [row[:] for row in self.pattern]  # Return deep copy
    
    def get_attack_pattern(self):
        """Get the attack pattern (only valid in dual mode)"""
        if self.dual_mode:
            _, attack_pattern = self.convert_single_to_dual()
            return attack_pattern
        return None
    
    def get_pattern(self):
        """Get the single pattern (for backward compatibility)"""
        return [row[:] for row in self.pattern]  # Return deep copy
    
    def set_movement_pattern(self, pattern):
        """Set the movement pattern"""
        if pattern and len(pattern) == 8 and len(pattern[0]) == 8:
            if self.dual_mode:
                # For dual mode, we need to merge with existing attack pattern
                _, attack_pattern = self.convert_single_to_dual()
                self.pattern = self.convert_dual_to_single(pattern, attack_pattern)
            else:
                self.pattern = [row[:] for row in pattern]  # Deep copy
            self.update_visual()
    
    def set_attack_pattern(self, pattern):
        """Set the attack pattern (only valid in dual mode)"""
        if self.dual_mode and pattern and len(pattern) == 8 and len(pattern[0]) == 8:
            # For dual mode, we need to merge with existing movement pattern
            movement_pattern, _ = self.convert_single_to_dual()
            self.pattern = self.convert_dual_to_single(movement_pattern, pattern)
            self.update_visual()

    def get_checkbox_states(self):
        """Get the current checkbox states"""
        return {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }


# Convenience functions for different use cases

def edit_single_pattern(initial_pattern=None, title="Edit Pattern", parent=None, checkbox_states=None):
    """
    Edit a single pattern (for piece editor compatibility)
    Returns: (pattern, checkbox_states) or (None, None) if cancelled
    """
    dialog = PatternEditorDialog(initial_pattern, None, title, parent, checkbox_states)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_pattern(), dialog.get_checkbox_states()
    return None, None

def edit_dual_patterns(movement_pattern=None, attack_pattern=None, title="Edit Movement & Attack Patterns", parent=None, checkbox_states=None):
    """
    Edit both movement and attack patterns (for ability editor)
    Returns: (movement_pattern, attack_pattern, checkbox_states) or (None, None, None) if cancelled
    """
    dialog = PatternEditorDialog(movement_pattern, attack_pattern, title, parent, checkbox_states)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_movement_pattern(), dialog.get_attack_pattern(), dialog.get_checkbox_states()
    return None, None

# Legacy compatibility - ensure all expected exports are available
__all__ = ['PatternEditorDialog', 'edit_single_pattern', 'edit_dual_patterns'], None