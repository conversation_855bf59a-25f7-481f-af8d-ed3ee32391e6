"""
Adventure Chess Widgets Package
Contains reusable UI widgets for editors and dialogs

!! NO VALIDATION POLICY !!
All widgets allow MAXIMUM CUSTOMIZATION with no validation constraints.
"""

# Import all widgets for easy access
from .file_operations_widget import FileOperationsWidget
from .inline_selectors_widget import InlineAbilitySelector, InlinePieceSelector
from .pattern_editor_widget import PatternEditorDialog
from .range_editor_widget import RangeEditorDialog
from .unified_adjacency_widget import edit_adjacency_config
from .piece_ability_manager_widget import PieceAbilityManagerDialog
from .batch_update_widget import BatchUpdateDialog

__all__ = [
    'FileOperationsWidget',
    'InlineAbilitySelector',
    'InlinePieceSelector', 
    'PatternEditorDialog',
    'RangeEditorDialog',
    'edit_adjacency_config',
    'PieceAbilityManagerDialog',
    'BatchUpdateDialog'
]
