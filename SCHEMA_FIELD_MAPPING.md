# Adventure Chess - Schema Field Mapping Documentation v1.0.5

## 🎯 **Overview**

This document provides comprehensive mapping between UI components, schema fields, and data formats in the Adventure Chess application after the Pydantic schema integration. Updated based on comprehensive analysis of ability_editor.py and piece_editor.py as master references.

## 📋 **Field Mapping Conventions**

### **Naming Patterns**
- **UI Widgets**: `snake_case` (e.g., `range_friendly_only_check`)
- **Schema Fields**: `snake_case` (e.g., `range_friendly_only`)
- **Legacy JSON**: `camelCase` (e.g., `rangeFriendlyOnly`)
- **Data Types**: Pydantic type annotations with validation

### **Data Type Mappings**

| UI Component | Schema Type | Description |
|--------------|-------------|-------------|
| `QLineEdit` | `str` | Text input with string validation |
| `QTextEdit` | `str` | Multi-line text with string validation |
| `QSpinBox` | `int` | Integer input with range validation |
| `QCheckBox` | `bool` | Boolean checkbox state |
| `QComboBox` | `Enum` or `str` | Dropdown with enum/string validation |
| `QRadioButton` | `bool` | Boolean radio button state |
| `InlinePieceSelector` | `List[Union[str, Dict]]` | Complex piece selection data |
| `InlineAbilitySelector` | `List[Union[str, Dict]]` | Complex ability selection data |
| Pattern Editor | `Pattern8x8` | 8x8 grid pattern with validation |
| Range Editor | `RangeMask8x8` | 8x8 boolean mask with validation |

## 🎯 **Piece Editor Field Mappings**

### **Basic Information**
```python
# UI Widget → Schema Field (Type) → Legacy JSON
name_edit → name (str) → name
desc_edit → description (str) → description
role_combo → role (PieceRole) → role  # Commander/Supporter only (Regular removed)
```

### **Special Properties**
```python
# Boolean Properties
can_castle_check → can_castle (bool) → canCastle
track_starting_position_check → track_starting_position (bool) → trackStartingPosition
color_directional_check → color_directional (bool) → colorDirectional
capture_yes/capture_no → can_capture (bool) → canCapture
```

### **Icons**
```python
# Icon Configuration
black_icon_combo → black_icon (str) → blackIcon
white_icon_combo → white_icon (str) → whiteIcon
# Note: icon_picker and icon_preview widgets are UI-only (not saved)
```

### **Movement Configuration**
```python
# Movement Model Sub-schema
move_combo → movement.type (MovementType) → movementType
ortho_range_spin → movement.distance (int) → orthogonalRange  # When type=Orthogonal
diag_range_spin → movement.distance (int) → diagonalRange    # When type=Diagonal
any_range_spin → movement.distance (int) → anyRange          # When type=Any
current_custom_pattern → movement.pattern (Pattern8x8) → customMovementPattern
custom_pattern_piece_pos → movement.piece_position (List[int]) → customMovementPiecePosition
```

### **Point System & Recharge Configuration**
```python
# Point System Fields
enable_recharge_check → enable_recharge (bool) → enableRecharge
max_points_spin → max_points (int) → maxPoints
starting_points_spin → starting_points (int) → startingPoints
recharge_combo → recharge_type (RechargeType) → rechargeType
turn_points_spin → turn_points (int) → turnPoints
capture_points_spin → capture_points (int) → capturePoints
move_points_spin → move_points (int) → movePoints
adjacency_recharge_btn → adjacency_recharge_config (Dict[str, Any]) → adjacencyRechargeConfig
committed_turns_spin → committed_recharge_turns (int) → committedRechargeTurns
```

### **Abilities and Promotions**
```python
# List Fields
abilities → abilities (List[str]) → abilities
primary_promotions → promotions (List[str]) → promotions
secondary_promotions → secondary_promotions (List[str]) → secondaryPromotions
# Note: promotion_btn and abilities_list widgets are UI-only (not saved)
```

## ⚡ **Ability Editor Field Mappings**

### **Core Fields**
```python
# Basic Information
name_edit → name (str) → name
description_edit → description (str) → description
cost_spin → cost (int) → cost
auto_cost_check → auto_cost_check (bool) → autoCostCheck
activation_combo → activation_mode (ActivationMode) → activationMode
tag_groups → tags (List[str]) → tags
```

### **Range Configuration (tag: "range")**
```python
range_pattern → range_mask (RangeMask8x8) → rangeMask
range_piece_position → piece_position (Coordinate) → piecePosition
range_friendly_only_check → range_friendly_only (bool) → rangeFriendlyOnly
range_enemy_only_check → range_enemy_only (bool) → rangeEnemyOnly
range_include_start_check → range_include_start (bool) → rangeIncludeStart
range_include_self_check → range_include_self (bool) → rangeIncludeSelf
```

### **Complex List Fields**
```python
# Piece Selection Lists (Union[str, Dict] format)
summon_selector → summon_list (List[Union[str, Dict]]) → summonList
carry_target_selector → carry_list (List[Union[str, Dict]]) → carryList
buff_target_selector → buff_target_list (List[Union[str, Dict]]) → buffTargetList
debuff_target_selector → debuff_target_list (List[Union[str, Dict]]) → debuffTargetList

# Ability Selection Lists
buff_ability_selector → buff_abilities (List[Union[str, Dict]]) → buffAbilities
debuff_prevent_ability_selector → debuff_prevent_abilities (List[Union[str, Dict]]) → debuffPreventAbilities
```

### **Pattern Data Fields**
```python
# Complex Pattern Data
adjacency_tile_pattern → adjacency_pattern (Dict[str, Any]) → adjacencyPattern
displace_custom_map → displace_custom_pattern (Dict[str, Any]) → displaceCustomPattern
fog_custom_range_pattern → fog_custom_range (Dict[str, Any]) → fogCustomRange
trap_teleport_pattern → trap_teleport_pattern (Dict[str, Any]) → trapTeleportPattern
```

## 🎨 **Dialog Data Collection Mappings**

### **Range Editor Dialog** (range_editor_dialog.py)
```python
# Range Editor Fields
pattern → range_mask (RangeMask8x8) → rangeMask
piece_pos → piece_position (Coordinate) → piecePosition
starting_square_check → range_include_starting_square (bool) → rangeIncludeStartingSquare
continue_off_board_check → range_continue_off_board (bool) → rangeContinueOffBoard
# Returns: (pattern, piece_position, checkbox_states) tuple
```

### **Pattern Editor Dialog** (pattern_editor_dialog.py)
```python
# Pattern Editor Fields
pattern → movement.pattern (Pattern8x8) → customMovementPattern
piece_pos → movement.piece_position (Coordinate) → customMovementPiecePosition
starting_square_check → starting_square_checked (bool) → startingSquareChecked
continue_off_board_check → continue_off_board_checked (bool) → continueOffBoardChecked
# Pattern values: 0=Empty, 1=Move, 2=Attack, 3=Both, 4=Action, 5=Any
```

### **Adjacency Editor Dialog** (adjacency_editor_dialog.py)
```python
# Adjacency Configuration
pieces → adjacency_list (List[Union[str, Dict]]) → adjacencyList
distance → adjacency_distance (int) → adjacencyDistance
pattern → adjacency_pattern (Dict[str, Any]) → adjacencyPattern
# Dynamic grid: 3x3 (distance=1) to 11x11 (distance=5)
```

### **Inline Selection Widgets** (dialogs/inline_selectors.py)
```python
# Piece Selection Format
{
    "name": "piece_name",           # str: Piece name or "Any"/"Friendly"/"Enemy"
    "cost": 5,                      # int: Cost value
    "no_cost": false,               # bool: Whether cost is disabled
    "tags": ["friendly", "pawn"]    # List[str]: Tag prefixes
}

# Ability Selection Format
{
    "name": "ability_name",         # str: Ability name
    "cost": 3,                      # int: Cost value
    "no_cost": false                # bool: Whether cost is disabled
}
```

## 🔄 **Data Conversion Flow**

### **UI to Schema (Pydantic Bridge)**
1. **Widget Collection**: `collect_widget_data()` or `collect_all_piece_widget_data()` gathers UI values
2. **Bridge Processing**: `PydanticBridge.get_piece_data_from_ui()` or `get_ability_data_from_ui()`
3. **Migration**: `DataMigrationManager.migrate_*_dict_to_model()` converts to Pydantic models
4. **Validation**: Pydantic validates against schema models with type checking
5. **Legacy Format**: `model.to_legacy_dict()` converts back for UI compatibility

### **Schema to UI (Pydantic Bridge)**
1. **Data Loading**: `PydanticDataManager.load_piece()` or `load_ability()` from JSON files
2. **Bridge Processing**: `PydanticBridge.set_piece_data_to_ui()` or `set_ability_data_to_ui()`
3. **Migration**: Automatic upgrading of legacy data formats
4. **Widget Setting**: `set_widget_values_from_data()` or `set_all_piece_widget_values()` populates UI
5. **State Restoration**: Dialog states and complex data restored from `dialog_states` field

### **Legacy Compatibility & Migration**
1. **Bidirectional Mapping**: camelCase ↔ snake_case conversion via `from_legacy_dict()` and `to_legacy_dict()`
2. **Data Migration**: `DataMigrationManager` automatically upgrades old formats
3. **Backward Compatibility**: Existing save files work seamlessly with `CompatibilityLayer`
4. **Forward Compatibility**: New fields added without breaking old data using `extra_fields`
5. **Field Normalization**: Handles both camelCase and snake_case field names in input data

## 📊 **Validation Features**

### **Type Safety**
- **Enum Validation**: MovementType, RechargeType, ActivationMode, PieceRole
- **Range Validation**: Integer fields with min/max constraints
- **Pattern Validation**: 8x8 grids with coordinate validation
- **List Validation**: Complex piece/ability selection formats

### **Business Rules**
- **Conditional Fields**: Hidden fields validated only when visible
- **Cross-Field Validation**: Related fields validated together
- **Tag-Specific Validation**: Each ability tag has custom validation
- **Data Integrity**: Ensures consistent and valid configurations

### **Error Reporting**
- **Clear Messages**: Human-readable validation error descriptions
- **Field-Specific**: Errors pinpoint exact problematic fields
- **Contextual Help**: Suggestions for fixing validation issues
- **Graceful Fallbacks**: System continues working with invalid data

## 🚀 **Benefits of Schema Integration**

### **Developer Experience**
- **IDE Support**: Full autocomplete and type checking
- **Clear Documentation**: Self-documenting schema fields
- **Error Prevention**: Catch issues at development time
- **Refactoring Safety**: Type system prevents breaking changes

### **Data Integrity**
- **Validation**: All data validated against schemas
- **Consistency**: Uniform data formats across application
- **Migration**: Automatic upgrading of old data formats
- **Reliability**: Prevents invalid configurations from being saved

### **Performance**
- **Efficient Validation**: Fast Pydantic validation with Rust backend
- **Caching**: Validated models cached for performance
- **Batch Operations**: Multiple validations processed efficiently
- **Memory Optimization**: Efficient data structures and serialization

