#!/usr/bin/env python3
"""
Unified Adjacency Dialog for Adventure Chess
Combines InlinePieceSelector with custom grid pattern system for all adjacency needs
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QPushButton, QLabel, QGroupBox, QSpinBox, QWidget
)
from PyQt6.QtCore import Qt

# Import shared UI utilities
from ui.ui_shared_components import create_dialog_buttons
from dialogs.inline_selectors import InlinePieceSelector


class UnifiedAdjacencyDialog(QDialog):
    """
    Unified dialog for all adjacency configuration needs
    Used for both piece editor recharge and ability editor adjacency tags
    """
    
    def __init__(self, parent=None, initial_config=None, dialog_type="adjacency_required"):
        super().__init__(parent)
        self.dialog_type = dialog_type  # "adjacency_required" or "adjacency_recharge"
        
        if dialog_type == "adjacency_recharge":
            self.setWindowTitle("Adjacency Recharge Configuration")
        else:
            self.setWindowTitle("Adjacency Required Configuration")
            
        self.setMinimumSize(500, 600)
        
        # Initialize configuration
        self.config = initial_config if initial_config else {
            'pieces': [],
            'distance': 1,
            'pattern': set()  # Set of relative positions (row_offset, col_offset)
        }
        
        # Initialize adjacency selection tracking
        self.adjacency_selected = set()
        if 'pattern' in self.config:
            self.adjacency_selected = set(self.config['pattern'])
        
        self.init_ui()
        self.load_config()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Instructions
        if self.dialog_type == "adjacency_recharge":
            instructions = QLabel("Configure which pieces must be adjacent for recharge to work.")
        else:
            instructions = QLabel("Configure which pieces must be adjacent for this ability to activate.")
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(instructions)
        
        # Piece Selector Section
        pieces_group = QGroupBox("Required Adjacent Pieces")
        pieces_layout = QVBoxLayout(pieces_group)
        
        # Use inline piece selector with costs for recharge
        allow_costs = self.dialog_type == "adjacency_recharge"
        self.piece_selector = InlinePieceSelector(
            self, 
            "Adjacent Pieces", 
            allow_costs=allow_costs
        )
        self.piece_selector.pieces_changed.connect(self.on_pieces_changed)
        pieces_layout.addWidget(self.piece_selector)
        
        layout.addWidget(pieces_group)
        
        # Distance and Pattern Section
        pattern_group = QGroupBox("Adjacency Pattern")
        pattern_layout = QVBoxLayout(pattern_group)
        
        # Distance configuration
        distance_layout = QFormLayout()
        
        self.distance_spin = QSpinBox()
        self.distance_spin.setRange(1, 5)
        self.distance_spin.setValue(1)
        self.distance_spin.setToolTip("Maximum distance for adjacency (1=3x3, 2=5x5, etc.)")
        self.distance_spin.valueChanged.connect(self.update_grid_size)
        distance_layout.addRow("Max Distance:", self.distance_spin)
        
        pattern_layout.addLayout(distance_layout)
        
        # Pattern instructions
        pattern_desc = QLabel("Select which squares around the piece are valid for adjacency:")
        pattern_desc.setWordWrap(True)
        pattern_desc.setStyleSheet("color: #666; font-style: italic; padding: 5px;")
        pattern_layout.addWidget(pattern_desc)
        
        # Grid pattern selector
        self.grid_widget = QWidget()
        self.grid_widget.setFixedSize(300, 300)
        self.grid_widget.setStyleSheet("border: 1px solid #ccc; background: white;")
        pattern_layout.addWidget(self.grid_widget)
        
        # Create grid layout
        self.grid_layout = QGridLayout(self.grid_widget)
        self.grid_layout.setSpacing(2)
        
        # Initialize grid
        self.grid_buttons = []
        self.update_grid_size()
        
        layout.addWidget(pattern_group)
        
        # Dialog buttons
        button_layout = create_dialog_buttons(self)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def update_grid_size(self):
        """Update the grid size based on distance"""
        # Clear existing grid
        for i in reversed(range(self.grid_layout.count())): 
            widget = self.grid_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        
        self.grid_buttons = []
        distance = self.distance_spin.value()
        grid_size = 2 * distance + 1  # e.g., distance 1 = 3x3, distance 2 = 5x5
        center = distance
        
        for r in range(grid_size):
            row = []
            for c in range(grid_size):
                btn = QPushButton()
                btn.setFixedSize(25, 25)
                btn.setCheckable(True)
                
                # Center piece (always checked and disabled)
                if r == center and c == center:
                    btn.setChecked(True)
                    btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold; color: white;")
                    btn.setText("♔")
                    btn.setEnabled(False)
                else:
                    # Make clickable for adjacency selection
                    btn.clicked.connect(lambda checked, row=r, col=c: self.toggle_tile(row, col, checked))
                    
                    # Check if this position was previously selected
                    relative_pos = (r - center, c - center)
                    if relative_pos in self.adjacency_selected:
                        btn.setChecked(True)
                    
                    self.update_tile_style(btn, r, c, center)
                
                self.grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.grid_buttons.append(row)
    
    def toggle_tile(self, row, col, checked):
        """Toggle adjacency tile selection"""
        distance = self.distance_spin.value()
        center = distance
        relative_pos = (row - center, col - center)
        
        if checked:
            self.adjacency_selected.add(relative_pos)
        else:
            self.adjacency_selected.discard(relative_pos)
        
        # Update the tile style
        btn = self.grid_buttons[row][col]
        self.update_tile_style(btn, row, col, center)
    
    def update_tile_style(self, btn, row, col, center):
        """Update the style of a grid tile"""
        is_light = (row + col) % 2 == 0
        relative_pos = (row - center, col - center)
        
        if relative_pos in self.adjacency_selected:
            # Selected tile
            btn.setStyleSheet("background: #90EE90; border: 2px solid #228B22; font-weight: bold;")
            btn.setText("✓")
        else:
            # Unselected tile - checkerboard pattern
            if is_light:
                btn.setStyleSheet("background: #f0f0f0; border: 1px solid #ccc;")
            else:
                btn.setStyleSheet("background: #e0e0e0; border: 1px solid #ccc;")
            btn.setText("")
    
    def on_pieces_changed(self):
        """Handle piece selection changes"""
        # Update config when pieces change
        self.config['pieces'] = self.piece_selector.get_selected_pieces()
    
    def load_config(self):
        """Load configuration into the dialog"""
        if 'pieces' in self.config:
            self.piece_selector.set_selected_pieces(self.config['pieces'])
        
        if 'distance' in self.config:
            self.distance_spin.setValue(self.config['distance'])
        
        if 'pattern' in self.config:
            self.adjacency_selected = set(self.config['pattern'])
            self.update_grid_size()  # Refresh grid with loaded pattern
    
    def get_config(self):
        """Get the current configuration"""
        return {
            'pieces': self.piece_selector.get_selected_pieces(),
            'distance': self.distance_spin.value(),
            'pattern': list(self.adjacency_selected)  # Convert set to list for JSON serialization
        }


def edit_adjacency_config(parent=None, initial_config=None, dialog_type="adjacency_required"):
    """
    Convenience function to edit adjacency configuration
    
    Args:
        parent: Parent widget
        initial_config: Initial configuration dict
        dialog_type: "adjacency_required" or "adjacency_recharge"
    
    Returns:
        Configuration dict if accepted, None if cancelled
    """
    dialog = UnifiedAdjacencyDialog(parent, initial_config, dialog_type)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_config()
    return None


# Legacy compatibility functions
def edit_adjacency_recharge_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency recharge"""
    return edit_adjacency_config(parent, initial_config, "adjacency_recharge")


def edit_adjacency_required_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency required"""
    return edit_adjacency_config(parent, initial_config, "adjacency_required")
