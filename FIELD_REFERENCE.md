# Adventure Chess - Complete Field Reference v1.0.5
## 📋 **SCHEMA INTEGRATION COMPLETE - PYDANTIC SYSTEM**

> **Data Management**: Unified Pydantic system with full validation and bridge layer integration

---

## 🎯 **PIECE EDITOR FIELDS**

### **Basic Information**
- `name_edit` (QLineEdit) → `name` (str) - Piece name
- `desc_edit` (QTextEdit) → `description` (str) - Piece description

### **Role & Properties**
- `role_combo` (QComboBox) → `role` (PieceRole) - Piece role (Commander, Supporter)
    - `can_castle_check` (QCheckBox) → `can_castle` (bool) - Hidden field, shown only for Commander role
- `track_starting_position_check` (QCheckBox) → `track_starting_position` (bool) - Track if piece has moved

### **Icons**
- `black_icon_combo` (QComboBox) → `black_icon` (str) - Black piece icon filename
- `white_icon_combo` (QComboBox) → `white_icon` (str) - White piece icon filename
- `black_icon_picker` (QPushButton) - File picker for black icon (not saved)
- `white_icon_picker` (QPushButton) - File picker for white icon (not saved)
- `black_icon_preview` (QLabel) - Preview display 40x40 (not saved)
- `white_icon_preview` (QLabel) - Preview display 40x40 (not saved)

### **Movement Pattern** (Pydantic Movement Model)
- `move_combo` (QComboBox) → `movement.type` (MovementType) - Movement type enum (Orthogonal/Diagonal/Any/L-shape/Custom)
    - `ortho_range_spin` (QSpinBox) → `movement.distance` (int) - Range for orthogonal movement (0-8)
    - `diag_range_spin` (QSpinBox) → `movement.distance` (int) - Range for diagonal movement (0-8)
    - `any_range_spin` (QSpinBox) → `movement.distance` (int) - Range for any movement (0-8)
    - `custom_pattern_btn` (QPushButton) → `movement.pattern` (Pattern8x8) - Opens pattern editor for custom movement
- `capture_yes` (QRadioButton) → `can_capture` (bool) - Can capture pieces (true)
- `capture_no` (QRadioButton) → `can_capture` (bool) - Cannot capture pieces (false)
- `color_directional_check` (QCheckBox) → `color_directional` (bool) - Different movement for white/black

### **Points & Recharge System** (Pydantic Recharge Model)
- `enable_recharge_check` (QCheckBox) → `enable_recharge` (bool) - Enable recharge system
    - `max_points_spin` (QSpinBox) → `max_points` (int) - Maximum action points (0-99)
    - `starting_points_spin` (QSpinBox) → `starting_points` (int) - Starting action points (0-99)
    - `recharge_combo` (QComboBox) → `recharge_type` (RechargeType) - Recharge type enum (Turn/Adjacency/Committed)
        - `turn_points_spin` (QSpinBox) → `turn_points` (int) - Points per turn (0-99)
        - `capture_points_spin` (QSpinBox) → `capture_points` (int) - Points per capture (0-99)
        - `move_points_spin` (QSpinBox) → `move_points` (int) - Points per move (0-99)
        - `adjacency_recharge_btn` (QPushButton) → `adjacency_recharge_config` (dict) - Opens adjacency configuration dialog
        - `committed_turns_spin` (QSpinBox) → `committed_recharge_turns` (int) - Committed turns (0-99)

### **Promotion**
- `primary_promotions` (list) → `promotions` (List[str]) - List of primary promotion pieces
- `secondary_promotions` (list) → `secondary_promotions` (List[str]) - List of secondary promotion pieces
- `primary_promo_btn` (QPushButton) - Opens promotion selector dialog
- `secondary_promo_btn` (QPushButton) - Opens secondary promotion selector dialog
- `primary_promotion_display` (QLabel) - Display only (not saved)
- `secondary_promotion_display` (QLabel) - Display only (not saved)

### **Abilities**
- `abilities` (list) → `abilities` (List[str]) - List of ability filenames (internal storage)
- `abilities_list` (QListWidget) - UI display of abilities (not saved)
- `manage_abilities_btn` (QPushButton) - Opens piece ability manager dialog

### **File Operations**
- `file_ops_widget` (FileOperationsWidget) - Shared file operations component
- `piece_search_edit` (QLineEdit) - Search bar for piece list filtering
- `piece_list_widget` (QListWidget) - List of available pieces for loading

### **Custom Movement Pattern** (Hidden Data)
- `current_custom_pattern` → `movement.pattern` (Pattern8x8) - 8x8 grid pattern with validation (values 0-5)
- `custom_pattern_piece_pos` → `movement.piece_position` (Coordinate) - Validated [row, col] position

---

## ⚡ **ABILITY EDITOR FIELDS** (Pydantic Ability Schema)

### **Basic Information Tab**
- `name_edit` (QLineEdit) → `name` (str) - Ability name (1-50 chars)
- `description_edit` (QTextEdit) → `description` (str) - Ability description (max 500 chars)
- `cost_spin` (QSpinBox) → `cost` (int) - Ability cost (0-99)
- `auto_cost_check` (QCheckBox) → `auto_cost_check` (bool) - Auto calculate cost from complexity
- `activation_combo` (QComboBox) → `activation_mode` (ActivationMode) - auto/manual/reaction enum

### **Tags Tab**
- `tag_groups` (dict) → `tags` (List[str]) - Dictionary of tag checkboxes
    - Each checkbox maps to a tag name in the `tags` array
    - **28 Canonical Tags**: All validated against ABILITY_TAGS in config.py
    - Tags determine which configuration sections appear in Configuration Tab

### **Configuration Tab** (Dynamic based on selected tags)

#### **Range Configuration** (tag: "range")
- `range_pattern` → `rangeMask` (RangeMask8x8) - 8x8 boolean grid with validation
- `range_piece_position` → `piecePosition` (Coordinate) - [x, y] position
- `range_friendly_only_check` (QCheckBox) → `rangeFriendlyOnly` (bool)
- `range_enemy_only_check` (QCheckBox) → `rangeEnemyOnly` (bool)
- `range_include_start_check` (QCheckBox) → `rangeIncludeStart` (bool)
- `range_include_self_check` (QCheckBox) → `rangeIncludeSelf` (bool)

#### **Area Effect Configuration** (tag: "areaEffect")
- `area_size_spin` (QSpinBox) → `areaSize` (int)
- `area_shape_combo` (QComboBox) → `areaShape` (str) - circle/square/cross

#### **Move Configuration** (tag: "move")
- `move_distance_spin` (QSpinBox) → `move_distance` (int)
- `move_direction_combo` (QComboBox) → `move_direction` (str)

#### **Summon Configuration** (tag: "summon")
- `summon_selector` (InlinePieceSelector) → `summonList` (List[Union[str, Dict]]) - Pieces with costs
- `summon_max_spin` (QSpinBox) → `summonMax` (int)

#### **Revival Configuration** (tag: "revival")
- `revival_target_selector` (InlinePieceSelector) → `revival_list` (List[Union[str, Dict]])
- `revival_sacrifice_check` (QCheckBox) → `revival_sacrifice` (bool)
- `revival_max_cost_spin` (QSpinBox) → `revival_max_cost` (int)
- `revival_with_points_check` (QCheckBox) → `revival_with_points` (bool)
    - `revival_points_spin` (QSpinBox) → `revival_points` (int) - Hidden unless with points checked
- `revival_starting_check` (QCheckBox) → `revival_starting` (bool)
- `revival_within_turn_spin` (QSpinBox) → `revival_within_turn` (int)

#### **Capture Configuration** (tag: "capture")
- `capture_target_combo` (QComboBox) → `captureTarget` (str) - Enemy/Friendly/Any

#### **Carry Piece Configuration** (tag: "carryPiece")
- `carry_target_selector` (InlinePieceSelector) → `carryList` (List[Union[str, Dict]])
- `carry_range_spin` (QSpinBox) → `carryRange` (int)
- `carry_drop_death_check` (QCheckBox) → `carryDropOnDeath` (bool)
- `carry_share_abilities_check` (QCheckBox) → `carryShareAbilities` (bool)
- `carry_starting_piece_check` (QCheckBox) → `carryStartingPiece` (bool)

#### **Swap Places Configuration** (tag: "swapPlaces")
- `swap_selector` (InlinePieceSelector) → `swap_list` (List[Union[str, Dict]])

#### **Pass Through Configuration** (tag: "passThrough")
- `pass_through_selector` (InlinePieceSelector) → `passThroughList` (List[Union[str, Dict]])
- `pass_through_capture_combo` (QComboBox) → `passThroughCapture` (str)

#### **Adjacency Required Configuration** (tag: "adjacencyRequired")
- `adjacency_selector` (InlinePieceSelector) → `adjacencyList` (List[Union[str, Dict]])
- `adjacency_distance_spin` (QSpinBox) → `adjacencyDistance` (int)
- `adjacency_tile_widget` - Custom tile selector
- `adjacency_tile_pattern` → `adjacencyPattern` (Dict[str, Any]) - Custom pattern data

#### **Line of Sight Configuration** (tag: "losRequired")
- `los_ignore_friendly_check` (QCheckBox) → `losIgnoreFriendly` (bool)
- `los_ignore_enemy_check` (QCheckBox) → `losIgnoreEnemy` (bool)
- `los_ignore_all_check` (QCheckBox) → `losIgnoreAll` (bool)
- `prevent_los_check` (QCheckBox) → `preventLos` (bool)

#### **No Turn Cost Configuration** (tag: "noTurnCost")
- `no_turn_cost_limit_spin` (QSpinBox) → `noTurnCostLimit` (int)

#### **Share Space Configuration** (tag: "shareSpace")
- `share_space_max_spin` (QSpinBox) → `shareSpaceMax` (int)
- `share_space_same_type_check` (QCheckBox) → `shareSpaceSameType` (bool)
- `share_space_friendly_check` (QCheckBox) → `shareSpaceFriendly` (bool)
- `share_space_enemy_check` (QCheckBox) → `shareSpaceEnemy` (bool)
- `share_space_any_check` (QCheckBox) → `shareSpaceAny` (bool)

#### **Delay Configuration** (tag: "delay")
- `delay_turn_check` (QCheckBox) → `delayTurn` (bool)
    - `delay_turn_spin` (QSpinBox) → `delayTurnAmount` (int) - Hidden unless turn delay checked
- `delay_action_check` (QCheckBox) → `delayAction` (bool)
    - `delay_action_spin` (QSpinBox) → `delayActionAmount` (int) - Hidden unless action delay checked

#### **Displace Piece Configuration** (tag: "displacePiece")
- `displace_target_selector` (InlinePieceSelector) → `displaceTargetList` (List[Union[str, Dict]])
- `displace_direction_combo` (QComboBox) → `displaceDirection` (str)
- `displace_distance_spin` (QSpinBox) → `displaceDistance` (int)
- `displace_target_combo` (QComboBox) → `displaceTargetType` (str)
- `displace_custom_check` (QCheckBox) → `displaceCustom` (bool)
- `displace_custom_btn` (QPushButton) - Opens pattern editor
- `displace_custom_map` → `displaceCustomPattern` (Dict[str, Any]) - Hidden pattern data

#### **Immobilize Configuration** (tag: "immobilize")
- `immobilize_target_selector` (InlinePieceSelector) → `immobilizeTargetList` (List[Union[str, Dict]])
- `immobilize_duration_spin` (QSpinBox) → `immobilizeDuration` (int)
- `immobilize_duration_check` (QCheckBox) → `immobilizeDurationEnabled` (bool)

#### **Pulse Effect Configuration** (tag: "pulseEffect")
- `pulse_interval_spin` (QSpinBox) → `pulseInterval` (int)
- `pulse_enable_check` (QCheckBox) → `pulseEnabled` (bool)

#### **Fog of War Configuration** (tag: "fogOfWar")
- `fog_vision_combo` (QComboBox) → `fogVisionType` (str)
- `fog_radius_spin` (QSpinBox) → `fogRadius` (int)
- `fog_custom_range_btn` (QPushButton) → `fogCustomRange` (Dict[str, Any]) - Opens range editor
- `fog_duration_spin` (QSpinBox) → `fogDuration` (int)
- `fog_cost_spin` (QSpinBox) → `fogCost` (int)

#### **Add/Remove Obstacle Configuration** (tags: "addObstacle", "removeObstacle")
- `obstacle_type_combo` (QComboBox) → `obstacleType` (str)
- `remove_obstacle_type_combo` (QComboBox) → `removeObstacleType` (str)

#### **Duplicate Configuration** (tag: "duplicate")
- `duplicate_limit_check` (QCheckBox) → `duplicateLimit` (bool)
    - `duplicate_limit_spin` (QSpinBox) → `duplicateLimitAmount` (int) - Hidden unless limit checked
- `duplicate_offset_btn` (QPushButton) → `duplicateOffset` (Dict[str, Any]) - Opens offset editor

#### **Convert Piece Configuration** (tag: "convertPiece")
- `convert_target_selector` (InlinePieceSelector) → `convertTargetList` (List[Union[str, Dict]])
- `convert_target_combo` (QComboBox) → `convertTargetType` (str)

#### **Reaction Configuration** (tag: "reaction")
- `reaction_target_selector` (InlinePieceSelector) → `reactionTargets` (List[Union[str, Dict]])
- `reaction_event_combo` (QComboBox) → `reactionEvent` (str)
- `reaction_uses_action_check` (QCheckBox) → `reactionUsesAction` (bool)

#### **Buff Piece Configuration** (tag: "buffPiece")
- `buff_target_selector` (InlinePieceSelector) → `buff_target_list` (List[Union[str, Dict]])
- `buff_ability_selector` (InlineAbilitySelector) → `buff_abilities` (List[Union[str, Dict]])
- `buff_duration_spin` (QSpinBox) → `buff_duration` (int)
- `buff_add_ability_check` (QCheckBox) → `buff_add_ability` (bool)
- `buff_adjust_movement_attack_check` (QCheckBox) → `buff_adjust_movement_attack` (bool)
- `buff_movement_attack_btn` (QPushButton) → `buff_movement_attack_data` (Dict[str, Any])

#### **Debuff Piece Configuration** (tag: "debuffPiece")
- `debuff_target_selector` (InlinePieceSelector) → `debuff_target_list` (List[Union[str, Dict]])
- `debuff_prevent_ability_selector` (InlineAbilitySelector) → `debuff_prevent_abilities` (List[Union[str, Dict]])
- `debuff_duration_spin` (QSpinBox) → `debuff_duration` (int)
- `debuff_prevent_ability_check` (QCheckBox) → `debuff_prevent_ability` (bool)
- `debuff_prevent_los_check` (QCheckBox) → `debuff_prevent_los` (bool)
- `debuff_adjust_movement_attack_check` (QCheckBox) → `debuff_adjust_movement_attack` (bool)
- `debuff_movement_attack_btn` (QPushButton) → `debuff_movement_attack_data` (Dict[str, Any])

#### **Invisible Configuration** (tag: "invisible")
- `invisible_reveal_move_check` (QCheckBox) → `invisibleRevealMove` (bool)
    - `invisible_reveal_move_spin` (QSpinBox) → `invisibleRevealMoveAmount` (int) - Hidden unless checked
- `invisible_reveal_capture_check` (QCheckBox) → `invisibleRevealCapture` (bool)
    - `invisible_reveal_capture_spin` (QSpinBox) → `invisibleRevealCaptureAmount` (int) - Hidden unless checked
- `invisible_reveal_action_check` (QCheckBox) → `invisibleRevealAction` (bool)
    - `invisible_reveal_action_spin` (QSpinBox) → `invisibleRevealActionAmount` (int) - Hidden unless checked
- `invisible_reveal_los_check` (QCheckBox) → `invisibleRevealLos` (bool)

#### **Trap Tile Configuration** (tag: "trapTile")
- `trap_capture_check` (QCheckBox) → `trapCapture` (bool)
- `trap_immobilize_check` (QCheckBox) → `trapImmobilize` (bool)
    - `trap_immobilize_spin` (QSpinBox) → `trapImmobilizeAmount` (int) - Hidden unless checked
- `trap_teleport_check` (QCheckBox) → `trapTeleport` (bool)
- `trap_teleport_btn` (QPushButton) → `trapTeleportPattern` (Dict[str, Any]) - Opens teleport editor
- `trap_add_ability_check` (QCheckBox) → `trapAddAbility` (bool)
- `trap_ability_btn` (QPushButton) → `trapAbility` (str) - Opens ability selector

#### **Starting Position Requirement** (tag: "requiresStartingPosition")
- No additional fields - just the tag presence

---

## 🎨 **DIALOG DATA COLLECTION PATTERNS**

### **Range Editor Dialog** (range_editor_dialog.py)
- `pattern` → `range_mask` (RangeMask8x8) - 8x8 boolean grid for range targeting
- `piece_pos` → `piece_position` (Coordinate) - [row, col] piece position in pattern
- `starting_square_check` → `range_include_starting_square` (bool) - Include starting square checkbox
- `continue_off_board_check` → `range_continue_off_board` (bool) - Continue off board checkbox
- **Quick Patterns**: Rook, Bishop, Queen, Knight, King, Global presets
- **Returns**: (pattern, piece_position, checkbox_states) or (None, None, None) if cancelled

### **Pattern Editor Dialog** (pattern_editor_dialog.py)
- `pattern` → `movement.pattern` (Pattern8x8) - 8x8 integer grid (values 0-5: Empty/Move/Attack/Both/Action/Any)
- `piece_pos` → `movement.piece_position` (Coordinate) - [row, col] piece position in pattern
- `starting_square_check` → `starting_square_checked` (bool) - Starting square checkbox state
- `continue_off_board_check` → `continue_off_board_checked` (bool) - Continue off board checkbox state
- **Dual Mode Support**: Separate movement and attack patterns
- **Paint Mode**: Click to set specific tile types
- **Quick Patterns**: Rook, Bishop, Queen, Knight, King, Global presets

### **Adjacency Editor Dialog** (adjacency_editor_dialog.py)
- `pieces` → `adjacency_list` (List[Union[str, Dict]]) - List of required adjacent pieces
- `distance` → `adjacency_distance` (int) - Adjacency distance (1-5, default 1)
- `pattern` → `adjacency_pattern` (Dict[str, Any]) - 11x11 grid pattern for max distance
- **Dynamic Grid**: Expands based on distance setting (3x3 for distance 1, up to 11x11 for distance 5)
- **Square-based Patterns**: Range 1 = 3x3 grid, Range 0 = self only

### **Piece Ability Manager Dialog** (piece_ability_manager.py)
- `piece_abilities` → `abilities` (List[str]) - List of ability filename references
- **Available Abilities List**: Shows all abilities from data/abilities directory
- **Piece Abilities List**: Shows currently assigned abilities with summary
- **Simple References**: Only stores ability filenames, not full ability data

### **Inline Selection Widgets** (dialogs/inline_selectors.py)
- **InlinePieceSelector**: Collects piece selections with costs and tags
- **InlineAbilitySelector**: Collects ability selections with costs
- **Tag Prefixes**: Support for "Any", "Friendly", "Enemy" + piece combinations
- **Cost Management**: Individual cost spinners with "no cost" checkbox options
- **Multi-Selection**: Support for multiple pieces/abilities with different costs

---

### **Streamlined Architecture** 🏗️
```
UI Widgets → collect_widget_data() → PydanticBridge → DataMigrationManager → Pydantic Models → PydanticDataManager → JSON Files
```

## 🔍 **PYDANTIC SCHEMA INTEGRATION PATTERNS**

### **Conditional Visibility Rules:**
1. **Role-based fields**: `can_castle_check` only visible when role is "Commander"
2. **Movement-based fields**: Range spinboxes only visible based on movement type selection
3. **Recharge-based fields**: All recharge fields hidden unless `enable_recharge_check` is checked
4. **Checkbox-dependent fields**: Many spinboxes are hidden unless their parent checkbox is checked
5. **Tag-dependent fields**: All configuration fields only appear when their corresponding tag is selected

### **Schema Field Naming Convention:**
- **UI widgets**: snake_case (`range_friendly_only_check`)
- **Schema fields**: snake_case with camelCase aliases (`range_friendly_only` → alias="rangeFriendlyOnly")
- **Data flow**: UI collects as camelCase, schema validates with aliases, stored as snake_case
- **Type validation**: All fields have Pydantic type annotations with proper aliases
- **Complex data**: Union types for flexible piece/ability selection

### **Data Validation Features:**
- **Type Safety**: All fields validated against Pydantic types
- **Enum Validation**: MovementType, RechargeType, ActivationMode, PieceRole
- **Pattern Validation**: Pattern8x8, RangeMask8x8, Coordinate types
- **List Validation**: Union[str, Dict] for complex piece/ability selections
- **Model Validation**: Cross-field validation and business rules

---
