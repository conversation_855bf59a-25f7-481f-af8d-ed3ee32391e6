#!/usr/bin/env python3
"""
File Operations Widget for Adventure Chess Editors
Provides uniform file operations across piece and ability editors

!! NO VALIDATION POLICY !!
This widget allows MAXIMUM CUSTOMIZATION with no validation constraints.
Auto-loading from dropdown provides immediate access to any file.
Save operations store ONLY what is displayed in the UI for full flexibility.
"""

from PyQt6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QPushButton, QComboBox, 
    QLabel, QMessageBox, QFileDialog
)
from PyQt6.QtCore import pyqtSignal, Qt
import os
from utils.simple_bridge import simple_bridge


class FileOperationsWidget(QWidget):
    """Uniform file operations widget for editors"""
    
    # Signals
    file_loaded = pyqtSignal(str)  # Emitted when a file is loaded (filename)
    file_saved = pyqtSignal(str)   # Emitted when a file is saved (filename)
    new_file = pyqtSignal()        # Emitted when new file is created
    
    def __init__(self, parent=None, file_type="piece"):
        super().__init__(parent)
        self.file_type = file_type  # "piece" or "ability"
        self.parent_editor = parent
        self.setup_ui()
        self.refresh_file_list()
        
    def setup_ui(self):
        """Setup the file operations UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # File selector dropdown
        selector_layout = QVBoxLayout()
        selector_layout.addWidget(QLabel(f"Load {self.file_type.title()}:"))
        
        self.file_combo = QComboBox()
        self.file_combo.setMinimumWidth(200)
        self.file_combo.currentTextChanged.connect(self.on_file_selected)
        selector_layout.addWidget(self.file_combo)
        
        layout.addLayout(selector_layout)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        # New button
        self.new_btn = QPushButton("📄 New")
        self.new_btn.setToolTip(f"Create a new {self.file_type}")
        self.new_btn.clicked.connect(self.new_file_clicked)
        button_layout.addWidget(self.new_btn)
        
        # Save button
        self.save_btn = QPushButton("💾 Save")
        self.save_btn.setToolTip(f"Save current {self.file_type}")
        self.save_btn.clicked.connect(self.save_file_clicked)
        button_layout.addWidget(self.save_btn)
        
        # Save As button
        self.save_as_btn = QPushButton("💾 Save As")
        self.save_as_btn.setToolTip(f"Save {self.file_type} with new name")
        self.save_as_btn.clicked.connect(self.save_as_file_clicked)
        button_layout.addWidget(self.save_as_btn)
        
        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.setToolTip("Refresh file list")
        self.refresh_btn.clicked.connect(self.refresh_file_list)
        button_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
    def refresh_file_list(self):
        """Refresh the file dropdown list"""
        self.file_combo.blockSignals(True)  # Prevent auto-loading during refresh
        self.file_combo.clear()
        
        # Add default separator
        self.file_combo.addItem("-----")
        
        try:
            if self.file_type == "piece":
                files = simple_bridge.list_pieces()
            else:  # ability
                files = simple_bridge.list_abilities()
                
            for filename in sorted(files):
                # Load file to get display name
                try:
                    if self.file_type == "piece":
                        data, error = simple_bridge.load_piece_for_ui(filename)
                    else:
                        data, error = simple_bridge.load_ability_for_ui(filename)
                        
                    if data and not error:
                        display_name = data.get('name', filename)
                        self.file_combo.addItem(f"{display_name} ({filename})", filename)
                    else:
                        self.file_combo.addItem(f"❌ {filename}", filename)
                except:
                    self.file_combo.addItem(f"❌ {filename}", filename)
                    
        except Exception as e:
            print(f"Error refreshing {self.file_type} list: {e}")
            
        self.file_combo.blockSignals(False)
        
    def on_file_selected(self, text):
        """Handle file selection from dropdown - auto-load"""
        if text == "-----" or not text:
            return
            
        # Get the actual filename from item data
        current_index = self.file_combo.currentIndex()
        if current_index <= 0:  # Skip separator
            return
            
        filename = self.file_combo.itemData(current_index)
        if filename:
            self.load_file(filename)
            
    def load_file(self, filename):
        """Load a file and emit signal"""
        try:
            if self.parent_editor and hasattr(self.parent_editor, 'load_data'):
                success = self.parent_editor.load_data(filename)
                if success:
                    self.file_loaded.emit(filename)
                    print(f"✓ Loaded {self.file_type}: {filename}")
                else:
                    print(f"❌ Failed to load {self.file_type}: {filename}")
            else:
                print(f"❌ Parent editor missing load_data method")
        except Exception as e:
            print(f"❌ Error loading {self.file_type} {filename}: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load {self.file_type}:\n{str(e)}")
            
    def new_file_clicked(self):
        """Handle new file button click"""
        if self.parent_editor and hasattr(self.parent_editor, 'reset_form'):
            self.parent_editor.reset_form()
            self.file_combo.setCurrentIndex(0)  # Reset to separator
            self.new_file.emit()
            print(f"✓ Created new {self.file_type}")
        else:
            print(f"❌ Parent editor missing reset_form method")
            
    def save_file_clicked(self):
        """Handle save button click"""
        if self.parent_editor and hasattr(self.parent_editor, 'save_data'):
            success = self.parent_editor.save_data()
            if success:
                filename = getattr(self.parent_editor, 'current_filename', 'unknown')
                self.file_saved.emit(filename)
                self.refresh_file_list()  # Refresh list after save
                print(f"✓ Saved {self.file_type}: {filename}")
        else:
            print(f"❌ Parent editor missing save_data method")
            
    def save_as_file_clicked(self):
        """Handle save as button click"""
        if self.parent_editor and hasattr(self.parent_editor, 'save_data'):
            # Get new filename from user
            if self.file_type == "piece":
                from config import PIECES_DIR
                directory = PIECES_DIR
            else:
                from config import ABILITIES_DIR  
                directory = ABILITIES_DIR
                
            filename, _ = QFileDialog.getSaveFileName(
                self, f"Save {self.file_type.title()} As", 
                directory, "JSON Files (*.json)"
            )
            
            if filename:
                # Remove .json extension for save_data method
                base_filename = os.path.basename(filename).replace('.json', '')
                success = self.parent_editor.save_data(base_filename)
                if success:
                    self.file_saved.emit(base_filename)
                    self.refresh_file_list()  # Refresh list after save
                    print(f"✓ Saved {self.file_type} as: {base_filename}")
        else:
            print(f"❌ Parent editor missing save_data method")
            
    def set_current_file(self, filename):
        """Set the current file in the dropdown"""
        for i in range(self.file_combo.count()):
            item_data = self.file_combo.itemData(i)
            if item_data == filename:
                self.file_combo.blockSignals(True)
                self.file_combo.setCurrentIndex(i)
                self.file_combo.blockSignals(False)
                break
