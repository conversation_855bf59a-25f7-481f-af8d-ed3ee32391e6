#!/usr/bin/env python3
"""
Script to update imports from dialogs to widgets folder
"""

import os
import re
from pathlib import Path

# Mapping of old imports to new imports
IMPORT_MAPPINGS = {
    'from dialogs.inline_selectors import': 'from widgets.inline_selectors_widget import',
    'from dialogs.pattern_editor_dialog import': 'from widgets.pattern_editor_widget import',
    'from dialogs.range_editor_dialog import': 'from widgets.range_editor_widget import',
    'from dialogs.unified_adjacency_dialog import': 'from widgets.unified_adjacency_widget import',
    'from dialogs.piece_ability_manager import': 'from widgets.piece_ability_manager_widget import',
    'from dialogs.batch_update_dialog import': 'from widgets.batch_update_widget import',
}

def update_file_imports(file_path):
    """Update imports in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply all import mappings
        for old_import, new_import in IMPORT_MAPPINGS.items():
            content = content.replace(old_import, new_import)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Updated imports in: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def main():
    """Update all imports in the project"""
    print("🔄 Updating imports from dialogs to widgets...")
    
    # Directories to scan
    directories = ['editors', 'widgets', 'main.py']
    
    updated_files = 0
    
    for directory in directories:
        if os.path.isfile(directory):
            # Single file
            if update_file_imports(directory):
                updated_files += 1
        else:
            # Directory
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        if update_file_imports(file_path):
                            updated_files += 1
    
    print(f"\n✅ Import update complete! Updated {updated_files} files.")

if __name__ == "__main__":
    main()
