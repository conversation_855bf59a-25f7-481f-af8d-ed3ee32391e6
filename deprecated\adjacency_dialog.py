#!/usr/bin/env python3
"""
Adjacency Dialog for Adventure Chess
Unified dialog for adjacency configuration used in both piece editor and ability editor
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QLabel, QGroupBox
)
from PyQt6.QtCore import Qt

# Import shared UI utilities
from ui.ui_shared_components import create_dialog_buttons
from dialogs.inline_selectors import InlinePieceSelector
from dialogs.range_editor_dialog import TargetRangeDialog


class AdjacencyDialog(QDialog):
    """
    Unified dialog for adjacency configuration
    Used for both piece editor recharge and ability editor adjacency tags
    """
    
    def __init__(self, parent=None, initial_config=None, dialog_type="adjacency_required"):
        super().__init__(parent)
        self.dialog_type = dialog_type  # "adjacency_required" or "adjacency_recharge"
        
        if dialog_type == "adjacency_recharge":
            self.setWindowTitle("Adjacency Recharge Configuration")
        else:
            self.setWindowTitle("Adjacency Required Configuration")
            
        self.setMinimumSize(500, 400)
        
        # Initialize configuration
        self.config = initial_config if initial_config else {
            'pieces': [],
            'rangePattern': [[False for _ in range(8)] for _ in range(8)],
            'rangePiecePosition': [3, 3]
        }
        
        self.init_ui()
        self.load_config()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Instructions
        if self.dialog_type == "adjacency_recharge":
            instructions = QLabel("Configure which pieces must be adjacent for recharge and their target range:")
        else:
            instructions = QLabel("Configure which pieces must be adjacent for this ability and their target range:")
        
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(instructions)
        
        # Piece Selector Section
        pieces_group = QGroupBox("Required Adjacent Pieces")
        pieces_layout = QVBoxLayout(pieces_group)
        
        # Use inline piece selector
        allow_costs = self.dialog_type == "adjacency_recharge"
        self.piece_selector = InlinePieceSelector(
            self, 
            "Adjacent Pieces", 
            allow_costs=allow_costs
        )
        pieces_layout.addWidget(self.piece_selector)
        
        layout.addWidget(pieces_group)
        
        # Target Range Section
        range_group = QGroupBox("Target Range")
        range_layout = QVBoxLayout(range_group)
        
        # Range description
        range_desc = QLabel("Define the range pattern where adjacent pieces must be located:")
        range_desc.setWordWrap(True)
        range_desc.setStyleSheet("color: #666; font-style: italic; padding: 5px;")
        range_layout.addWidget(range_desc)
        
        # Range editor button and preview
        range_button_layout = QHBoxLayout()
        
        self.edit_range_btn = QPushButton("🎯 Edit Target Range")
        self.edit_range_btn.clicked.connect(self.open_range_editor)
        self.edit_range_btn.setToolTip("Open target range editor to define where adjacent pieces must be")
        range_button_layout.addWidget(self.edit_range_btn)
        
        # Range preview (simple text for now)
        self.range_preview = QLabel("Default: 3x3 square around piece")
        self.range_preview.setStyleSheet("padding: 5px; background-color: #2d3748; border: 1px solid #4a5568; border-radius: 3px;")
        range_button_layout.addWidget(self.range_preview)
        
        range_layout.addLayout(range_button_layout)
        layout.addWidget(range_group)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.ok_btn = QPushButton("OK")
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setDefault(True)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)

    def open_range_editor(self):
        """Open the target range editor"""
        dialog = TargetRangeDialog(
            initial_pattern=self.config.get('rangePattern'),
            piece_position=self.config.get('rangePiecePosition', [3, 3]),
            title="Edit Adjacency Target Range",
            parent=self
        )
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.config['rangePattern'] = dialog.get_range_pattern()
            self.config['rangePiecePosition'] = dialog.get_piece_position()
            self.update_range_preview()

    def update_range_preview(self):
        """Update the range preview text"""
        pattern = self.config.get('rangePattern', [])
        if pattern:
            # Count selected squares
            selected_count = sum(sum(row) for row in pattern)
            if selected_count == 0:
                self.range_preview.setText("No target squares selected")
            elif selected_count == 8:  # 3x3 minus center
                self.range_preview.setText("3x3 square around piece")
            else:
                self.range_preview.setText(f"Custom pattern ({selected_count} squares)")
        else:
            self.range_preview.setText("Default: 3x3 square around piece")

    def load_config(self):
        """Load configuration into the UI"""
        # Load pieces into selector
        pieces = self.config.get('pieces', [])
        if hasattr(self.piece_selector, 'set_pieces'):
            self.piece_selector.set_pieces(pieces)
        
        # Update range preview
        self.update_range_preview()

    def get_config(self):
        """Get the current configuration"""
        # Get pieces from selector
        pieces = []
        if hasattr(self.piece_selector, 'get_pieces'):
            pieces = self.piece_selector.get_pieces()
        
        return {
            'pieces': pieces,
            'rangePattern': self.config.get('rangePattern', [[False for _ in range(8)] for _ in range(8)]),
            'rangePiecePosition': self.config.get('rangePiecePosition', [3, 3])
        }


def edit_adjacency_config(parent=None, initial_config=None, dialog_type="adjacency_required"):
    """
    Convenience function to edit adjacency configuration
    
    Args:
        parent: Parent widget
        initial_config: Initial configuration dict
        dialog_type: "adjacency_required" or "adjacency_recharge"
    
    Returns:
        Configuration dict if accepted, None if cancelled
    """
    dialog = AdjacencyDialog(parent, initial_config, dialog_type)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_config()
    return None


# Legacy compatibility functions
def edit_adjacency_recharge_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency recharge"""
    return edit_adjacency_config(parent, initial_config, "adjacency_recharge")


def edit_adjacency_required_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency required"""
    return edit_adjacency_config(parent, initial_config, "adjacency_required")
