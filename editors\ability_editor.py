"""
Ability Editor for Adventure Chess
Create, edit, and save abilities as separate JSON files

ORGANIZATION:
- Basic Tab: Name, description, cost, activation mode
- Tags Tab: Ability tag selection checkboxes
- Configuration Tab: Dynamic configuration based on selected tags

STRUCTURE:
1. Initialization & UI Creation
2. Basic Tab Functionality
3. Tags Tab Functionality
4. Configuration Tab Functionality
5. Ability Tag Configurations (organized by tag type)
6. List Management (organized by ability tag)
7. Pattern Editors
8. Event Handlers
9. File Operations
10. Data Management
"""
# Standard library imports
import os

# PyQt6 imports - only what we actually use
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton,
    QComboBox, QCheckBox, QSpinBox, QListWidget, QGroupBox,
    QGridLayout, QScrollArea, QDialog, QFormLayout,
    QMessageBox, QFileDialog, QTab<PERSON><PERSON>t, Q<PERSON>rame,
    QSizePolicy, QRadioButton
)
from PyQt6.QtCore import Qt

# Local imports - grouped by functionality
from config import (
    ABILITIES_DIR, ABILITY_TAGS, ACTIVATION_MODES,
    ABILITY_EDITOR_DEFAULT, ABILITY_EDITOR_MIN, DISPLACEMENT_DIRECTIONS,
    OBSTACLE_TYPES, REACTION_EVENT_TYPES, VISION_TYPES
)
from utils.utils import sanitize_filename
from ui.ui_utils import (
    ResponsiveScrollArea, ResponsiveLayout, setup_responsive_window,
    make_widget_responsive, TabWidgetResponsive
)

# Data management - using only Pydantic system (old managers removed)
# Shared UI components
from ui.ui_shared_components import FileOperationsWidget, ValidationStatusWidget, AreaEffectGridWidget

# Data management - using only Pydantic system
from utils.simple_bridge import simple_bridge

# Streamlined data management
from editors.base_editor import BaseEditor



class AreaEffectMaskDialog(QDialog):
    """Editor for creating area effect masks (8x8 grid)"""
    def __init__(self, parent=None, initial_mask=None):
        super().__init__(parent)
        self.setWindowTitle("Area Effect Pattern Editor")
        self.setMinimumSize(400, 500)
        
        # Initialize mask and target position
        self.mask = initial_mask if initial_mask else [[False for _ in range(8)] for _ in range(8)]
        self.target_pos = [3, 3]  # Default center position
        
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel("Click tiles to toggle area effect. Right-click to set target square (🎯).")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Create custom grid widget
        self.grid_widget = AreaEffectGridWidget(initial_mask=self.mask, target_pos=self.target_pos)
        layout.addWidget(self.grid_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        save_btn = QPushButton("Save")
        cancel_btn = QPushButton("Cancel")
        save_btn.clicked.connect(self.accept)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def get_mask(self):
        """Get the current mask"""
        return self.grid_widget.get_boolean_mask()
    
    def get_target_pos(self):
        """Get the current target position"""
        return self.grid_widget.get_target_position()

class AbilityEditorWindow(BaseEditor):
    """
    Adventure Chess Ability Editor

    Organized into clear sections:
    - Basic Tab: Core ability properties
    - Tags Tab: Ability tag selection
    - Configuration Tab: Dynamic configuration based on selected tags
    """

    # ========== INITIALIZATION ==========

    def __init__(self):
        # Initialize base editor with ability data type
        super().__init__("ability")

        self.setWindowTitle("Ability Editor - Adventure Chess")

        # Setup responsive window
        setup_responsive_window(self, ABILITY_EDITOR_DEFAULT, ABILITY_EDITOR_MIN)

        # Legacy compatibility - redirect to base class properties
        self.current_ability = self.current_data

        self.init_ui()
        self.reset_form()
    
    def init_ui(self):
        """Initialize the user interface"""
        main_layout = ResponsiveLayout.create_vbox()
        
        # Top toolbar with shared file operations
        toolbar_layout = ResponsiveLayout.create_hbox(margin=5, spacing=5)

        # Create shared file operations widget
        self.file_ops_widget = FileOperationsWidget("full")

        # Connect signals to ability-specific methods
        self.file_ops_widget.connect_signals(
            new_func=self.new_ability,
            load_func=self.load_ability,
            save_func=self.save_ability,
            save_as_func=self.save_as_ability,
            delete_func=self.delete_current_ability
        )

        # Customize button text for ability-specific operations
        self.file_ops_widget.new_btn.setText("📄 New Ability")
        self.file_ops_widget.load_btn.setText("📂 Load Ability")
        self.file_ops_widget.save_btn.setText("💾 Save Ability")
        self.file_ops_widget.delete_btn.setText("🗑️ Delete Ability")
        self.file_ops_widget.delete_btn.setStyleSheet("QPushButton { color: #d32f2f; }")

        # Load combo for ability search/selection
        self.load_combo = QComboBox()
        self.load_combo.setEditable(True)
        self.load_combo.setPlaceholderText("Search abilities...")
        self.load_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.load_combo.setMinimumHeight(30)
        self.load_combo.currentTextChanged.connect(self.on_load_selection_changed)

        toolbar_layout.addWidget(self.file_ops_widget)
        toolbar_layout.addWidget(QLabel("Load:"))
        toolbar_layout.addWidget(self.load_combo)
        toolbar_layout.addStretch()
        
        main_layout.addLayout(toolbar_layout)
        
        # Main content in tabs
        self.tab_widget = QTabWidget()
        TabWidgetResponsive.setup_tab_widget(self.tab_widget)
        make_widget_responsive(self.tab_widget)
        
        # Basic Info Tab
        basic_tab = self.create_basic_tab()
        self.tab_widget.addTab(basic_tab, "Basic Info")
        
        # Tags Tab
        tags_tab = self.create_tags_tab()
        self.tab_widget.addTab(tags_tab, "Ability Tags")
        
        # Configuration Tab (dynamic based on selected tags)
        config_tab = self.create_config_tab()
        self.tab_widget.addTab(config_tab, "Configuration")
        

        
        main_layout.addWidget(self.tab_widget)
        
        # Status display using shared component
        self.status_widget = ValidationStatusWidget()
        main_layout.addWidget(self.status_widget)

        # Keep status_label for backward compatibility (redirect to status_widget)
        self.status_label = self.status_widget.status_label

        # Create central widget and set layout (QMainWindow uses setCentralWidget, not setLayout)
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
        # Initialize ability list
        self.refresh_ability_list()
        
        # Initialization complete

    # ========== ZOOM FUNCTIONALITY ==========

    def zoom_in(self):
        """Zoom in the editor content"""
        # Find all scroll areas and zoom them
        scroll_areas = self.findChildren(ResponsiveScrollArea)
        for scroll_area in scroll_areas:
            scroll_area.zoom_in()

    def zoom_out(self):
        """Zoom out the editor content"""
        # Find all scroll areas and zoom them
        scroll_areas = self.findChildren(ResponsiveScrollArea)
        for scroll_area in scroll_areas:
            scroll_area.zoom_out()

    def reset_zoom(self):
        """Reset zoom to 100%"""
        # Find all scroll areas and reset zoom
        scroll_areas = self.findChildren(ResponsiveScrollArea)
        for scroll_area in scroll_areas:
            scroll_area.reset_zoom()

    def create_basic_tab(self):
        """Create the basic info tab"""
        # Create scrollable content
        scroll_area = ResponsiveScrollArea()
        
        # Basic fields
        form_layout = ResponsiveLayout.create_form_layout()
        
        # Name field
        self.name_edit = QLineEdit()
        self.name_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        
        # Description field
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        # Description with summary button
        desc_layout = ResponsiveLayout.create_hbox(margin=0, spacing=5)
        desc_layout.addWidget(self.description_edit)
        self.summary_btn = QPushButton("📝 Summarize")
        self.summary_btn.clicked.connect(self.show_summary_message)
        self.summary_btn.setToolTip("Will update when we are fleshed out and ready for your flesh ;)")
        desc_layout.addWidget(self.summary_btn)

        form_layout.addRow("Ability Name:", self.name_edit)
        form_layout.addRow("Description:", desc_layout)
        
        scroll_area.add_layout(form_layout)
        
        # Cost Configuration
        cost_layout = ResponsiveLayout.create_hbox(margin=0, spacing=10)
        
        cost_layout.addWidget(QLabel("Cost:"))
        self.cost_spin = QSpinBox()
        self.cost_spin.setRange(0, 999)
        self.cost_spin.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)

        cost_layout.addWidget(self.cost_spin)
        
        self.auto_cost_check = QCheckBox("Logic engine determines cost from complexity")
        self.auto_cost_check.setToolTip(
            "When checked, the logic engine will automatically determine if you have multiple abilities/targets "
            "and accurately enable your ability based on the individual costs listed in the JSON file. "
            "This displays the list of all individual costs in the cost field."
        )
        self.auto_cost_check.stateChanged.connect(self.on_auto_cost_changed)
        cost_layout.addWidget(self.auto_cost_check)
        
        cost_layout.addStretch()
        scroll_area.add_layout(cost_layout)
        
        # Activation Mode - Updated per glossary V1.0.1 (removed triggeredOnMove)
        activation_layout = ResponsiveLayout.create_hbox(margin=0, spacing=10)
        activation_layout.addWidget(QLabel("Activation Mode:"))
        self.activation_combo = QComboBox()
        self.activation_combo.addItems([mode[0] for mode in ACTIVATION_MODES])
        self.activation_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        activation_layout.addWidget(self.activation_combo)
        activation_layout.addStretch()
        scroll_area.add_layout(activation_layout)
        
        scroll_area.add_stretch()
        return scroll_area

    # ========== BASIC TAB FUNCTIONALITY ==========

    def show_summary_message(self):
        """Show summary message per glossary V1.0.1"""
        QMessageBox.information(self, "Summary Feature", "Will update when we are fleshed out and ready for your flesh ;)")
        print("Summary feature - coming soon.")

    # ========== TAGS TAB FUNCTIONALITY ==========

    def create_tags_tab(self):
        """Create the ability tags tab"""
        # Create scrollable content
        scroll_area = ResponsiveScrollArea()

        # Add instructions at the top
        instructions = QLabel("💡 Tip: Click checkboxes to select/unselect ability tags. Selected tags will show configuration options in the Configuration tab.")
        instructions.setWordWrap(True)
        instructions.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                color: #2c3e50;
                padding: 10px;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                margin: 5px;
                font-size: 11px;
            }
        """)
        scroll_area.add_widget(instructions)

        # Create tag groups
        self.tag_groups = {}
        
        # Create tag groups from centralized config
        tag_groups_config = [
            ("🟢 Action Tags", ABILITY_TAGS['action']),
            ("🟡 Targeting Tags", ABILITY_TAGS['targeting']),
            ("🔵 Condition Tags", ABILITY_TAGS['condition']),
            ("🔶 Special Tags", ABILITY_TAGS['special'])
        ]
        
        for group_name, tags in tag_groups_config:
            group = QGroupBox(group_name)
            group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            group_layout = ResponsiveLayout.create_grid(margin=5, spacing=5)
            
            for i, (tag, tooltip) in enumerate(tags):
                cb = QCheckBox(tag)
                cb.setToolTip(tooltip)
                cb.stateChanged.connect(self.on_tag_changed)
                cb.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
                self.tag_groups[tag] = cb
                group_layout.addWidget(cb, i // 2, i % 2)
            
            group.setLayout(group_layout)
            scroll_area.add_widget(group)
        
        scroll_area.add_stretch()
        return scroll_area

    # ========== CONFIGURATION TAB FUNCTIONALITY ==========

    def create_config_tab(self):
        """Create the configuration tab"""
        # Use responsive scroll area directly
        self.config_scroll_area = ResponsiveScrollArea()

        # Store reference to the content layout for dynamic updates
        self.config_content_layout = self.config_scroll_area.content_layout

        return self.config_scroll_area

    def on_tag_changed(self):
        """Handle tag checkbox changes - update configuration tab"""
        self.update_configuration_ui()
        if hasattr(self, 'auto_cost_check') and self.auto_cost_check.isChecked():
            self.calculate_auto_cost()
    
    def update_configuration_ui(self):
        """Update the configuration UI based on selected tags"""
        # Clear existing configuration widgets
        while self.config_content_layout.count():
            child = self.config_content_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
        
        # Reset widget references to avoid accessing deleted widgets
        # NOTE: Old list widgets (summon_list, swap_list, etc.) have been replaced with inline selectors
        # Only keeping current widget references that are still in use
        widget_attrs = [
            # Current inline selectors (these get recreated each time)
            'summon_selector', 'buff_target_selector', 'buff_ability_selector',
            'swap_selector', 'adjacency_selector', 'displace_target_selector',
            'immobilize_target_selector', 'convert_target_selector', 'revival_target_selector',
            'carry_target_selector', 'reaction_target_selector', 'debuff_target_selector',
            'debuff_prevent_ability_selector', 'pass_through_selector',

            # Configuration widgets that get recreated
            'summon_max_spin', 'carry_range_spin', 'carry_drop_death_check',
            'no_turn_cost_limit_spin', 'share_space_max_spin', 'area_size_spin', 'area_shape_combo',
            'capture_target_combo', 'pass_through_capture_combo',
            'adjacency_distance_spin', 'los_ignore_friendly_check', 'prevent_los_check',
            'delay_turns_spin', 'share_space_same_type_check',

            # Ability tag widgets
            'displace_direction_combo', 'displace_distance_spin', 'displace_target_combo',
            'displace_custom_check', 'displace_custom_btn', 'displace_custom_map',
            'immobilize_duration_spin', 'immobilize_target_combo',
            'pulse_interval_spin', 'pulse_enable_check',
            'reveal_radius_spin', 'reveal_duration_spin',
            'obstacle_type_combo', 'remove_obstacle_type_combo',
            'duplicate_limit_check', 'duplicate_limit_spin', 'duplicate_offset_btn',
            'convert_target_combo',

            # Advanced ability tag widgets
            'reaction_event_combo', 'reaction_uses_action_check',
            'buff_duration_spin', 'buff_add_ability_check',
            'buff_adjust_movement_attack_check', 'buff_movement_attack_btn',
            'debuff_duration_spin', 'debuff_prevent_ability_check',
            'debuff_prevent_los_check', 'debuff_adjust_movement_attack_check', 'debuff_movement_attack_btn',
            'invisible_reveal_move_check', 'invisible_reveal_move_spin', 'invisible_reveal_capture_check',
            'invisible_reveal_capture_spin', 'invisible_reveal_action_check', 'invisible_reveal_action_spin',
            'invisible_reveal_los_check',
            'trap_capture_check', 'trap_immobilize_check', 'trap_immobilize_spin',
            'trap_teleport_check', 'trap_teleport_btn', 'trap_add_ability_check', 'trap_ability_btn', 'trap_ability_label',
            'revival_sacrifice_check', 'revival_max_cost_spin', 'revival_with_points_check',
            'revival_points_spin', 'revival_starting_check', 'revival_within_turn_spin',
            'range_include_start_check', 'range_include_self_check',
            'los_ignore_enemy_check', 'los_ignore_all_check',
            'delay_turn_check', 'delay_turn_spin', 'delay_action_check', 'delay_action_spin',
            'fog_vision_combo', 'fog_radius_spin', 'fog_custom_range_btn', 'fog_duration_spin', 'fog_cost_spin',
            'carry_share_abilities_check', 'carry_starting_piece_btn', 'carry_starting_piece_label',
            'immobilize_duration_check',
            'adjacency_tile_widget', 'adjacency_tile_buttons', 'adjacency_tile_pattern'
        ]
        
        for attr in widget_attrs:
            if hasattr(self, attr):
                widget = getattr(self, attr)
                if widget is not None:
                    try:
                        # Safely disconnect any signals before deletion
                        if hasattr(widget, 'disconnect'):
                            widget.disconnect()
                    except (RuntimeError, TypeError):
                        # Widget already deleted or no connections
                        pass
                setattr(self, attr, None)
        
        # Get selected tags
        selected_tags = []
        for tag, checkbox in self.tag_groups.items():
            if checkbox.isChecked():
                selected_tags.append(tag)

        if not selected_tags:
            no_tags_label = QLabel("Select ability tags to see configuration options.")
            no_tags_label.setStyleSheet("color: #999; font-style: italic; padding: 20px;")
            no_tags_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.config_content_layout.addWidget(no_tags_label)
            return

        # Add configuration sections for each selected tag
        for tag in selected_tags:
            self.add_config_for_tag(tag)

        # Load current ability data into newly created configuration widgets
        if hasattr(self, 'current_ability') and self.current_ability:
            self.load_configuration_data()

        # Add stretch at the end
        self.config_content_layout.addStretch()

    def load_configuration_data(self):
        """Load current ability data into configuration widgets after they're created"""
        if hasattr(self, 'current_ability') and self.current_ability:
            try:
                # Directly set widget values from current_ability data
                self.set_widget_values_from_data(self.current_ability)

                # Update inline selectors with current data (safely)
                try:
                    self.update_inline_selectors_with_data()
                except Exception as selector_error:
                    print(f"Warning: Could not update inline selectors: {selector_error}")

            except Exception as e:
                print(f"Error loading configuration data: {e}")
                self.status_widget.show_warning("Some configuration data could not be loaded")

    # set_widget_values_from_data() is now inherited from BaseEditor and uses EditorDataInterface
    # This eliminates the duplicate widget setting pattern

    # ========== STREAMLINED DATA MANAGEMENT ==========
    # Using BaseEditor's standardized data collection

    # collect_widget_data() is now inherited from BaseEditor and uses EditorDataInterface
    # This eliminates the duplicate data collection pattern

    # Old collect_widget_data method removed - now using BaseEditor's standardized approach

    def refresh_list_displays(self):
        """Safely update inline selectors with current data"""
        try:
            # Only update inline selectors, don't try to refresh other widgets
            self.update_inline_selectors_with_data()
        except Exception as e:
            print(f"Warning: Could not update inline selectors: {e}")

    def add_config_for_tag(self, tag):
        """Unified method to add configuration for any tag with uncheck functionality"""
        # Map tags to their configuration methods and titles
        tag_config_map = {
            "range": (self.add_range_config, "🎯 Range Configuration"),
            "areaEffect": (self.add_area_effect_config, "💥 Area Effect Configuration"),
            "move": (self.add_move_config, "🚶 Move Configuration"),
            "summon": (self.add_summon_config, "✨ Summon Configuration"),
            "revival": (self.add_revival_config, "⚰️ Revival Configuration"),
            "capture": (self.add_capture_config, "⚔️ Capture Configuration"),
            "carryPiece": (self.add_carry_piece_config, "🎒 Carry Piece Configuration"),
            "swapPlaces": (self.add_swap_places_config, "🔄 Swap Places Configuration"),
            "passThrough": (self.add_pass_through_config, "🚶 Pass Through Configuration"),
            "adjacencyRequired": (self.add_adjacency_required_config, "🤝 Adjacency Required Configuration"),
            "losRequired": (self.add_los_required_config, "👁️ Line of Sight Required Configuration"),
            "noTurnCost": (self.add_no_turn_cost_config, "🆓 No Turn Cost Configuration"),
            "shareSpace": (self.add_share_space_config, "👥 Share Space Configuration"),
            "delay": (self.add_delay_config, "⏰ Delay Configuration"),
            "displacePiece": (self.add_displace_piece_config, "↗️ Displace Piece Configuration"),
            "immobilize": (self.add_immobilize_config, "🔒 Immobilize Configuration"),
            "pulseEffect": (self.add_pulse_effect_config, "📡 Pulse Effect Configuration"),
            "fogOfWar": (self.add_fog_of_war_config, "🌫️ Fog of War Configuration"),
            "addObstacle": (self.add_add_obstacle_config, "🧱 Add Obstacle Configuration"),
            "removeObstacle": (self.add_remove_obstacle_config, "🗑️ Remove Obstacle Configuration"),
            "duplicate": (self.add_duplicate_config, "👥 Duplicate Configuration"),
            "convertPiece": (self.add_convert_piece_config, "🔄 Convert Piece Configuration"),
            "reaction": (self.add_reaction_config, "⚡ Reaction Configuration"),
            "buffPiece": (self.add_buff_piece_config, "⬆️ Buff Piece Configuration"),
            "debuffPiece": (self.add_debuff_piece_config, "⬇️ Debuff Piece Configuration"),
            "invisible": (self.add_invisible_config, "👻 Invisible Configuration"),
            "trapTile": (self.add_trap_tile_config, "🪤 Trap Tile Configuration"),
            "requiresStartingPosition": (self.add_requires_starting_position_config, "🏠 Starting Position Requirement")
        }

        if tag in tag_config_map:
            config_method, title = tag_config_map[tag]
            self.add_config_section_with_uncheck_button(tag, title, config_method)

    def add_config_section_with_uncheck_button(self, tag, title, config_method):
        """Add a configuration section with an uncheck button in the title"""
        # Create a custom widget for the group box title with uncheck button
        title_widget = QWidget()
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)

        # Uncheck button on the left
        uncheck_btn = QPushButton("❌")
        uncheck_btn.setToolTip(f"Remove {tag} tag from this ability")
        uncheck_btn.setMaximumSize(20, 20)
        uncheck_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff6b6b;
                border: none;
                border-radius: 10px;
                font-size: 10px;
                color: white;
            }
            QPushButton:hover {
                background-color: #ff5252;
            }
        """)
        uncheck_btn.clicked.connect(lambda: self.uncheck_ability_tag(tag))
        title_layout.addWidget(uncheck_btn)

        # Title label
        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; margin-left: 5px;")
        title_layout.addWidget(title_label)

        title_layout.addStretch()
        title_widget.setLayout(title_layout)

        # Create the group box
        group = QGroupBox()
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        # Set the custom title widget
        group_layout = QVBoxLayout()
        group_layout.addWidget(title_widget)

        # Add a separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("color: #ccc;")
        group_layout.addWidget(separator)

        # Store current layout temporarily and call the config method
        temp_layout = self.config_content_layout
        self.config_content_layout = group_layout

        # Call the original config method
        config_method()

        # Restore layout
        self.config_content_layout = temp_layout

        group.setLayout(group_layout)
        self.config_content_layout.addWidget(group)

    def uncheck_ability_tag(self, tag):
        """Uncheck a specific ability tag and clean up related data"""
        if tag in self.tag_groups:
            self.tag_groups[tag].setChecked(False)
            # Clean up data related to this tag
            self.clean_tag_data(tag)
            # This will trigger on_tag_changed which updates the configuration UI

    def clean_tag_data(self, tag):
        """Clean up data related to a specific tag when it's unchecked"""
        # Tag-specific data cleanup mappings
        tag_cleanup_mappings = {
            'carryPiece': {
                'lists': ['carry_pieces', 'carry_targets'],
                'attributes': ['carry_range_pattern', 'carry_range_piece_position',
                              'carry_drop_range_pattern', 'carry_drop_range_piece_position']
            },
            'summon': {
                'lists': ['summon_pieces'],
                'attributes': []
            },
            'revival': {
                'lists': ['revival_pieces', 'revival_targets'],
                'attributes': []
            },
            'swapPlaces': {
                'lists': ['swap_pieces'],
                'attributes': []
            },
            'passThrough': {
                'lists': ['pass_through_pieces'],
                'attributes': ['pass_through_range_pattern', 'pass_through_piece_position']
            },
            'adjacencyRequired': {
                'lists': ['adjacency_pieces'],
                'attributes': ['adjacency_tile_pattern']
            },
            'displacePiece': {
                'lists': ['displace_targets'],
                'attributes': ['displace_custom_map']
            },
            'immobilize': {
                'lists': ['immobilize_targets'],
                'attributes': []
            },
            'convertPiece': {
                'lists': ['convert_targets'],
                'attributes': []
            },
            'buffPiece': {
                'lists': ['buff_targets', 'buff_abilities'],
                'attributes': []
            },
            'debuffPiece': {
                'lists': ['debuff_targets', 'debuff_prevent_abilities'],
                'attributes': []
            },
            'reaction': {
                'lists': ['reaction_targets'],
                'attributes': []
            },
            'range': {
                'lists': [],
                'attributes': ['range_pattern', 'range_piece_position', 'range_include_starting_square', 'range_continue_off_board']
            },
            'areaEffect': {
                'lists': [],
                'attributes': ['custom_area_pattern', 'area_continue_off_board']
            },
            'duplicate': {
                'lists': [],
                'attributes': ['duplicate_offset_pattern']
            },
            'trapTile': {
                'lists': [],
                'attributes': ['trap_teleport_range', 'trap_teleport_piece_position']
            }
        }

        if tag in tag_cleanup_mappings:
            cleanup_data = tag_cleanup_mappings[tag]

            # Clear lists
            for list_attr in cleanup_data['lists']:
                if hasattr(self, list_attr):
                    setattr(self, list_attr, [])
                    print(f"✓ Cleared {list_attr} for tag {tag}")

            # Clear attributes
            for attr in cleanup_data['attributes']:
                if hasattr(self, attr):
                    delattr(self, attr)
                    print(f"✓ Removed {attr} for tag {tag}")

            print(f"✓ Cleaned up data for unchecked tag: {tag}")

    # ========== ABILITY TAG CONFIGURATIONS ==========
    # Organized by tag type for easy navigation

    # --- Range and Targeting ---

    def add_range_config(self):
        """Add range configuration section"""
        group = QGroupBox("🎯 Range Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = ResponsiveLayout.create_vbox(margin=5)
        
        # Range pattern controls
        pattern_layout = ResponsiveLayout.create_hbox(margin=0, spacing=5)
        pattern_layout.addWidget(QLabel("Quick Patterns:"))
        
        # Chess piece pattern buttons with styling
        range_buttons = [
            ("♜", "rook", "Orthogonal lines (like Rook)"),
            ("♝", "bishop", "Diagonal lines (like Bishop)"),
            ("♛", "queen", "All directions (like Queen)"),
            ("♞", "knight", "L-shaped moves (like Knight)"),
            ("♚", "king", "Adjacent squares (like King)"),
            ("🌐", "global", "Entire board range")
        ]
        
        # Store pattern buttons for selection tracking
        self.range_pattern_buttons = []

        for symbol, preset_type, tooltip in range_buttons:
            btn = QPushButton(symbol)
            btn.setFixedSize(35, 30)
            btn.setToolTip(f"{tooltip} - Click to select this pattern")
            btn.setCheckable(True)  # Make buttons selectable
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #3a3a3a, stop:1 #2a2a2a);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a4a4a, stop:1 #3a3a3a);
                    border-color: #66aaff;
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4488cc, stop:1 #3366aa);
                    border-color: #66aaff;
                    border-width: 3px;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #1a1a1a, stop:1 #2a2a2a);
                    border-color: #4488cc;
                }
            """)
            btn.clicked.connect(lambda checked, pt=preset_type, b=btn: self.select_range_preset(pt, b))
            pattern_layout.addWidget(btn)
            self.range_pattern_buttons.append((btn, preset_type))
        
        pattern_layout.addStretch()
        
        self.clear_range_btn = QPushButton("Clear")
        self.clear_range_btn.clicked.connect(self.clear_range_selection)
        pattern_layout.addWidget(self.clear_range_btn)
        
        layout.addLayout(pattern_layout)
        
        # Custom Range Editor Button
        custom_range_layout = QHBoxLayout()
        self.custom_range_btn = QPushButton("Edit Custom Range Pattern")
        self.custom_range_btn.clicked.connect(self.edit_custom_range_pattern)
        self.custom_range_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        custom_range_layout.addWidget(self.custom_range_btn)
        custom_range_layout.addStretch()
        layout.addLayout(custom_range_layout)
        
        # Initialize range data
        if not hasattr(self, 'range_pattern'):
            self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        
        # Range options
        options_layout = ResponsiveLayout.create_form_layout()

        self.range_friendly_only_check = QCheckBox("Friendly side only")
        self.range_friendly_only_check.setToolTip("This ability can only be used on the friendly side of the map")
        options_layout.addRow("Options:", self.range_friendly_only_check)

        self.range_enemy_only_check = QCheckBox("Enemy side only")
        self.range_enemy_only_check.setToolTip("This ability can only be used on the enemy side of the map")
        options_layout.addRow("", self.range_enemy_only_check)
        
        # Make friendly/enemy mutually exclusive
        self.range_friendly_only_check.stateChanged.connect(self.on_range_side_changed)
        self.range_enemy_only_check.stateChanged.connect(self.on_range_side_changed)
        
        # Note: Starting tile inclusion is now handled in the range editor dialog
        # Users can use the "Include Starting Square" checkbox in the range editor
        
        layout.addLayout(options_layout)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize range data if needed
        if not hasattr(self, 'range_pattern'):
            self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        if not hasattr(self, 'range_piece_position'):
            self.range_piece_position = [4, 4]

        # Update preview
        self.update_range_preview()
    
    def add_area_effect_config(self):
        """Add area effect configuration section"""
        group = QGroupBox("💥 Area Effect Configuration")
        layout = QVBoxLayout()
        
        # Controls
        controls_layout = QFormLayout()
        
        self.area_size_spin = QSpinBox()
        self.area_size_spin.setRange(1, 8)
        self.area_size_spin.setValue(1)
        self.area_size_spin.setToolTip("Size of the area effect (1=1x1, 2=2x2, etc.)")
        self.area_size_spin.valueChanged.connect(self.update_area_effect_preview)
        controls_layout.addRow("Effect Size:", self.area_size_spin)
        
        self.area_shape_combo = QComboBox()
        self.area_shape_combo.addItems(["Circle", "Square", "Cross", "Line", "Custom"])
        self.area_shape_combo.setToolTip("Shape of the area effect")
        self.area_shape_combo.currentTextChanged.connect(self.update_area_effect_preview)
        self.area_shape_combo.currentTextChanged.connect(self.on_area_shape_changed)
        controls_layout.addRow("Effect Shape:", self.area_shape_combo)
        
        layout.addLayout(controls_layout)
        
        # Custom area effect button (initially hidden)
        self.custom_area_btn = QPushButton("Edit Custom Pattern")
        self.custom_area_btn.clicked.connect(self.edit_custom_area_effect)
        self.custom_area_btn.setVisible(False)
        layout.addWidget(self.custom_area_btn)
        
        # Instructions for interaction
        instructions = QLabel("Right-click: Move target (🎯) | Shift+Right-click: Move effect center")
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        layout.addWidget(instructions)
        
        # Visual preview
        preview_label = QLabel("Preview:")
        layout.addWidget(preview_label)
        
        self.area_effect_preview = QWidget()
        self.area_effect_preview.setFixedSize(200, 200)
        self.area_effect_preview.setStyleSheet("border: 1px solid #4a5568; background: #2d3748;")
        layout.addWidget(self.area_effect_preview)
        
        # Create mini grid for preview
        self.area_effect_grid = []
        grid_layout = QGridLayout(self.area_effect_preview)
        grid_layout.setSpacing(1)
        
        # Initialize target position and effect center separately
        self.area_effect_target = [4, 4]  # Where ability targets
        self.area_effect_center = [4, 4]  # Center of effect area
        
        for r in range(8):
            row = []
            for c in range(8):
                btn = QPushButton()
                btn.setFixedSize(22, 22)
                btn.setEnabled(True)
                btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                btn.customContextMenuRequested.connect(lambda pos, row=r, col=c: self.handle_area_effect_click(pos, row, col))
                grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.area_effect_grid.append(row)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # NOTE: Field initialization is now handled by CompleteDataManager.load_all_fields()
        # This is called automatically when ability data is loaded
        # Only initialize position and pattern data that needs special handling
        if 'areaEffectTarget' in self.current_ability:
            self.area_effect_target = self.current_ability['areaEffectTarget'][:]  # Copy
        else:
            self.area_effect_target = [4, 4]  # Default

        if 'areaEffectCenter' in self.current_ability:
            self.area_effect_center = self.current_ability['areaEffectCenter'][:]  # Copy
        else:
            self.area_effect_center = [4, 4]  # Default

        if 'customAreaPattern' in self.current_ability:
            self.custom_area_pattern = self.current_ability['customAreaPattern']
        else:
            self.custom_area_pattern = None
        
        # Initialize preview
        self.update_area_effect_preview()

    # --- Movement and Positioning ---

    def add_move_config(self):
        """Add move configuration section"""
        group = QGroupBox("🚶 Move Configuration")
        layout = QVBoxLayout()
        
        info_label = QLabel("This piece will move to the targeted square within range.")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(info_label)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)

    # --- Creation and Destruction ---

    def add_summon_config(self):
        """Add enhanced summon configuration section with inline selection"""
        group = QGroupBox("👥 Summon Configuration")
        layout = QVBoxLayout()

        # Enhanced inline piece selector
        from dialogs.inline_selectors import InlinePieceSelector
        self.summon_selector = InlinePieceSelector(self, "Summon Pieces", allow_costs=True)
        self.summon_selector.pieces_changed.connect(self.on_summon_pieces_changed)
        layout.addWidget(self.summon_selector)
        
        # Options
        options_layout = QFormLayout()
        
        self.summon_max_spin = QSpinBox()
        self.summon_max_spin.setRange(1, 10)
        self.summon_max_spin.setValue(1)
        self.summon_max_spin.setToolTip("Maximum pieces that can be summoned per use")
        options_layout.addRow("Max per Use:", self.summon_max_spin)
        
        layout.addLayout(options_layout)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize summon list if needed
        if not hasattr(self, 'summon_pieces'):
            self.summon_pieces = []
        # NOTE: List display updates are handled automatically by DialogDataManager
    


    def add_capture_config(self):
        """Add capture configuration section"""
        group = QGroupBox("⚔️ Capture Configuration")
        layout = QFormLayout()
        
        info_label = QLabel("Capture will destroy pieces when the ability is triggered.")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addRow(info_label)
        
        self.capture_target_combo = QComboBox()
        self.capture_target_combo.addItems(["Enemy", "Friendly", "Any"])
        self.capture_target_combo.setToolTip("Which pieces can be captured")
        layout.addRow("Target:", self.capture_target_combo)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)

    def add_swap_places_config(self):
        """Add swap places configuration section with inline selector"""
        group = QGroupBox("🔄 Swap Places Configuration")
        layout = QVBoxLayout()

        # Enhanced inline piece selector for swappable pieces
        from dialogs.inline_selectors import InlinePieceSelector
        self.swap_selector = InlinePieceSelector(self, "Swappable Pieces", allow_costs=True)
        self.swap_selector.pieces_changed.connect(self.on_swap_pieces_changed)
        layout.addWidget(self.swap_selector)

        group.setLayout(layout)
        self.config_content_layout.addWidget(group)

        # Initialize swap list if needed
        if not hasattr(self, 'swap_pieces'):
            self.swap_pieces = []
    
    
    def add_adjacency_required_config(self):
        """Add adjacency required configuration section"""
        group = QGroupBox("🤝 Adjacency Required Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()

        layout.addWidget(QLabel("Only activates when adjacent to specific pieces."))

        # Button to open unified adjacency dialog
        self.edit_adjacency_btn = QPushButton("🔗 Configure Adjacency Requirements")
        self.edit_adjacency_btn.clicked.connect(self.open_adjacency_dialog)
        self.edit_adjacency_btn.setToolTip("Open adjacency configuration dialog")
        layout.addWidget(self.edit_adjacency_btn)

        # Preview of current adjacency configuration
        self.adjacency_preview_label = QLabel("No adjacency requirements configured")
        self.adjacency_preview_label.setStyleSheet("color: #666; font-style: italic; padding: 10px; border: 1px solid #ddd; background: #f9f9f9;")
        self.adjacency_preview_label.setWordWrap(True)
        layout.addWidget(self.adjacency_preview_label)

        group.setLayout(layout)
        self.config_content_layout.addWidget(group)

        # Initialize adjacency data
        if not hasattr(self, 'adjacency_config'):
            self.adjacency_config = {
                'pieces': [],
                'distance': 1,
                'pattern': []
            }

        self.update_adjacency_preview_text()

    def open_adjacency_dialog(self):
        """Open the unified adjacency configuration dialog"""
        from dialogs.unified_adjacency_dialog import edit_adjacency_config

        result = edit_adjacency_config(
            parent=self,
            initial_config=self.adjacency_config,
            dialog_type="adjacency_required"
        )

        if result is not None:
            self.adjacency_config = result
            self.update_adjacency_preview_text()

    def update_adjacency_preview_text(self):
        """Update the adjacency preview text"""
        if not self.adjacency_config['pieces'] and not self.adjacency_config['pattern']:
            self.adjacency_preview_label.setText("No adjacency requirements configured")
        else:
            pieces_text = f"{len(self.adjacency_config['pieces'])} piece types" if self.adjacency_config['pieces'] else "Any pieces"
            pattern_text = f"{len(self.adjacency_config['pattern'])} specific squares" if self.adjacency_config['pattern'] else "all squares"
            distance_text = f"within distance {self.adjacency_config['distance']}"

            self.adjacency_preview_label.setText(
                f"Required: {pieces_text} in {pattern_text} {distance_text}"
            )
        if not hasattr(self, 'adjacency_tile_pattern') or self.adjacency_tile_pattern is None:
            self.adjacency_tile_pattern = [[False for _ in range(3)] for _ in range(3)]
        # NOTE: List display updates are handled automatically by DialogDataManager
        # Only update display if buttons are created
        if hasattr(self, 'adjacency_tile_buttons'):
            self.update_adjacency_tile_display()
    
    
    def add_no_turn_cost_config(self):
        """Add no turn cost configuration section"""
        group = QGroupBox("⚡ No Turn Cost Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("This ability does not consume turn points when used."))
        
        form_layout = QFormLayout()
        
        self.no_turn_cost_limit_spin = QSpinBox()
        self.no_turn_cost_limit_spin.setRange(0, 10)
        self.no_turn_cost_limit_spin.setValue(0)
        self.no_turn_cost_limit_spin.setToolTip("Maximum free uses per turn (0 = unlimited)")
        form_layout.addRow("Uses per Turn:", self.no_turn_cost_limit_spin)
        
        layout.addLayout(form_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    
    def add_share_space_config(self):
        """Add share space configuration section"""
        group = QGroupBox("📦 Share Space Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()

        layout.addWidget(QLabel("Allows multiple pieces to occupy the same square."))

        form_layout = QFormLayout()

        self.share_space_max_spin = QSpinBox()
        self.share_space_max_spin.setRange(2, 8)
        self.share_space_max_spin.setValue(2)
        self.share_space_max_spin.setToolTip("Maximum pieces per square")
        form_layout.addRow("Max Pieces:", self.share_space_max_spin)

        self.share_space_same_type_check = QCheckBox("Same type only")
        self.share_space_same_type_check.setToolTip("Only pieces of the same type can share")
        form_layout.addRow("Restrictions:", self.share_space_same_type_check)

        # Targeting options
        targeting_layout = QHBoxLayout()
        self.share_space_friendly_check = QCheckBox("Friendly")
        self.share_space_friendly_check.setChecked(True)
        self.share_space_friendly_check.setToolTip("Allow friendly pieces to share space")
        targeting_layout.addWidget(self.share_space_friendly_check)

        self.share_space_enemy_check = QCheckBox("Enemy")
        self.share_space_enemy_check.setToolTip("Allow enemy pieces to share space")
        targeting_layout.addWidget(self.share_space_enemy_check)

        self.share_space_any_check = QCheckBox("Any")
        self.share_space_any_check.setToolTip("Allow any pieces to share space")
        targeting_layout.addWidget(self.share_space_any_check)

        targeting_layout.addStretch()
        form_layout.addRow("Targeting:", targeting_layout)

        layout.addLayout(form_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    
    def add_displace_piece_config(self):
        """Add displace piece configuration section"""
        group = QGroupBox("🔄 Displace Piece Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Push a targeted piece in a direction or to specific positions."))
        
        form_layout = QFormLayout()
        
        # Direction selection
        self.displace_direction_combo = QComboBox()
        self.displace_direction_combo.addItems([f"{code} - {desc}" for code, desc in DISPLACEMENT_DIRECTIONS])
        self.displace_direction_combo.setToolTip("Direction to push the piece")
        form_layout.addRow("Direction:", self.displace_direction_combo)
        
        # Distance
        self.displace_distance_spin = QSpinBox()
        self.displace_distance_spin.setRange(1, 8)
        self.displace_distance_spin.setValue(1)
        self.displace_distance_spin.setToolTip("Distance to push the piece")
        form_layout.addRow("Distance:", self.displace_distance_spin)
        
        # Enhanced inline piece selector for displacement targets
        from dialogs.inline_selectors import InlinePieceSelector
        self.displace_target_selector = InlinePieceSelector(self, "Target Pieces", allow_costs=True)
        self.displace_target_selector.pieces_changed.connect(self.on_displace_targets_changed)
        form_layout.addRow("", self.displace_target_selector)
        
        layout.addLayout(form_layout)
        
        # Custom displacement map option
        self.displace_custom_check = QCheckBox("Use Custom Displacement Map")
        self.displace_custom_check.setToolTip("Define specific positions where pieces can be displaced")
        self.displace_custom_check.stateChanged.connect(self.on_displace_custom_changed)
        layout.addWidget(self.displace_custom_check)
        
        self.displace_custom_btn = QPushButton("Edit Custom Displacement Pattern")
        self.displace_custom_btn.clicked.connect(self.edit_custom_displacement_map)
        self.displace_custom_btn.setVisible(False)
        layout.addWidget(self.displace_custom_btn)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize custom displacement map
        if not hasattr(self, 'displace_custom_map'):
            self.displace_custom_map = [[False for _ in range(8)] for _ in range(8)]
        
        # Initialize displace targets list
        if not hasattr(self, 'displace_targets'):
            self.displace_targets = []
        # NOTE: List display updates are handled automatically by DialogDataManager
    
    def add_immobilize_config(self):
        """Add immobilize configuration section"""
        group = QGroupBox("🔒 Immobilize Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Prevent a piece from moving or acting for a set number of turns."))
        
        form_layout = QFormLayout()
        
        # Duration with checkbox
        duration_layout = QHBoxLayout()
        self.immobilize_duration_check = QCheckBox("Duration:")
        self.immobilize_duration_check.setChecked(True)
        self.immobilize_duration_check.stateChanged.connect(self.on_immobilize_duration_changed)
        duration_layout.addWidget(self.immobilize_duration_check)
        
        self.immobilize_duration_spin = QSpinBox()
        self.immobilize_duration_spin.setRange(0, 10)
        self.immobilize_duration_spin.setValue(2)
        self.immobilize_duration_spin.setSuffix(" turns")
        self.immobilize_duration_spin.setSpecialValueText("Indefinite")
        self.immobilize_duration_spin.setToolTip("Number of turns the piece is immobilized (0 = indefinite)")
        duration_layout.addWidget(self.immobilize_duration_spin)
        duration_layout.addStretch()
        form_layout.addRow("", duration_layout)
        
        # Enhanced inline piece selector for immobilize targets
        from dialogs.inline_selectors import InlinePieceSelector
        self.immobilize_target_selector = InlinePieceSelector(self, "Target Pieces", allow_costs=True)
        self.immobilize_target_selector.pieces_changed.connect(self.on_immobilize_targets_changed)
        form_layout.addRow("", self.immobilize_target_selector)
        
        layout.addLayout(form_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize immobilize targets list
        if not hasattr(self, 'immobilize_targets'):
            self.immobilize_targets = []
        # NOTE: List display updates are handled automatically by DialogDataManager
    

    
    def add_add_obstacle_config(self):
        """Add obstacle configuration section"""
        group = QGroupBox("🧱 Add Obstacle Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Spawns terrain obstacles like walls or spikes."))
        
        form_layout = QFormLayout()
        
        self.obstacle_type_combo = QComboBox()
        self.obstacle_type_combo.addItems([f"{code} - {desc}" for code, desc in OBSTACLE_TYPES])
        self.obstacle_type_combo.setToolTip("Type of obstacle to create")
        form_layout.addRow("Obstacle Type:", self.obstacle_type_combo)
        
        layout.addLayout(form_layout)
        layout.addWidget(QLabel("Use the Range configuration to define where obstacles can be placed."))
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    def add_remove_obstacle_config(self):
        """Add remove obstacle configuration section"""
        group = QGroupBox("🗑️ Remove Obstacle Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Removes obstacles in the targeted area."))
        
        form_layout = QFormLayout()
        
        self.remove_obstacle_type_combo = QComboBox()
        obstacle_options = [("any", "Any obstacle type")] + list(OBSTACLE_TYPES)
        self.remove_obstacle_type_combo.addItems([f"{code} - {desc}" for code, desc in obstacle_options])
        self.remove_obstacle_type_combo.setToolTip("Type of obstacles to remove (optional filter)")
        form_layout.addRow("Filter Type:", self.remove_obstacle_type_combo)
        
        layout.addLayout(form_layout)
        layout.addWidget(QLabel("Use the Range configuration to define the removal area."))
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    def add_duplicate_config(self):
        """Add duplicate configuration section"""
        group = QGroupBox("👥 Duplicate Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Creates a copy of the activating piece."))
        
        form_layout = QFormLayout()
        
        # Location offset button (reuses existing grid editor concept)
        self.duplicate_offset_btn = QPushButton("Edit Duplicate Positions")
        self.duplicate_offset_btn.clicked.connect(self.edit_duplicate_positions)
        self.duplicate_offset_btn.setToolTip("Define where duplicates can be placed")
        form_layout.addRow("Positions:", self.duplicate_offset_btn)
        
        # Limit option
        self.duplicate_limit_check = QCheckBox("Limit total duplicates")
        self.duplicate_limit_check.setToolTip("Set a maximum number of duplicates")
        self.duplicate_limit_check.stateChanged.connect(self.on_duplicate_limit_changed)
        form_layout.addRow("Limit:", self.duplicate_limit_check)
        
        self.duplicate_limit_spin = QSpinBox()
        self.duplicate_limit_spin.setRange(1, 10)
        self.duplicate_limit_spin.setValue(1)
        self.duplicate_limit_spin.setToolTip("Maximum number of duplicates")
        self.duplicate_limit_spin.setEnabled(False)
        form_layout.addRow("Max Duplicates:", self.duplicate_limit_spin)
        
        layout.addLayout(form_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize duplicate positions map
        if not hasattr(self, 'duplicate_positions_map'):
            self.duplicate_positions_map = [[False for _ in range(8)] for _ in range(8)]
    
    def add_convert_piece_config(self):
        """Add convert piece configuration section"""
        group = QGroupBox("🔄 Convert Piece Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Turn enemy units into friendly ones."))
        
        form_layout = QFormLayout()
        
        # Enhanced inline piece selector for conversion targets
        from dialogs.inline_selectors import InlinePieceSelector
        self.convert_target_selector = InlinePieceSelector(self, "Target Pieces", allow_costs=True)
        self.convert_target_selector.pieces_changed.connect(self.on_convert_targets_changed)
        form_layout.addRow("", self.convert_target_selector)
        
        layout.addLayout(form_layout)
        layout.addWidget(QLabel("Use the Range configuration to define targeting area."))
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize convert targets list
        if not hasattr(self, 'convert_targets'):
            self.convert_targets = []
        # NOTE: List display updates are handled automatically by inline selectors

    def clear_range_mask(self):
        """Clear the range mask"""
        self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        self.range_piece_position = [4, 4]  # Reset to center
        # NOTE: Range data is automatically saved by Pydantic data management
        self.update_range_preview()
    
    def edit_custom_range_pattern(self):
        """Edit custom range pattern using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get current checkbox states from ability data
        checkbox_states = self.current_ability.get('rangeCheckboxStates', {})

        pattern, piece_position, new_checkbox_states = edit_range_pattern(
            initial_pattern=self.range_pattern,
            piece_position=self.range_piece_position,
            title="Edit Range Pattern",
            parent=self,
            checkbox_states=checkbox_states
        )

        if pattern is not None:
            self.range_pattern = pattern
            self.range_piece_position = piece_position
            # Save checkbox states to ability data
            self.current_ability['rangeCheckboxStates'] = new_checkbox_states
            # NOTE: Range data is automatically saved by CompleteDataManager
            self.update_range_preview()
    
    # NOTE: Range data loading/saving is now handled by CompleteDataManager
    # These methods have been removed as they are redundant
    
    def apply_range_preset(self, preset_type):
        """Apply a preset range pattern based on chess piece movement with auto continue off board"""
        # Clear current pattern
        self.range_pattern = [[False for _ in range(8)] for _ in range(8)]

        piece_r, piece_c = self.range_piece_position

        # Auto-set continue off board based on preset type
        # King and Knight should NOT continue off board, all others should
        # Note: This is stored in the ability data and used when opening range editor
        should_continue_off_board = preset_type not in ['king', 'knight']
        # Store this setting for when the range editor is opened
        self.auto_continue_off_board = should_continue_off_board
        
        if preset_type == "rook":
            # Rook: All squares in orthogonal lines
            # Horizontal line
            for c in range(8):
                if c != piece_c:
                    self.range_pattern[piece_r][c] = True
            # Vertical line
            for r in range(8):
                if r != piece_r:
                    self.range_pattern[r][piece_c] = True
        
        elif preset_type == "bishop":
            # Bishop: All squares in diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        # Check if on diagonal
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.range_pattern[r][c] = True
        
        elif preset_type == "queen":
            # Queen: Combination of rook and bishop
            # Orthogonal lines (rook)
            for c in range(8):
                if c != piece_c:
                    self.range_pattern[piece_r][c] = True
            for r in range(8):
                if r != piece_r:
                    self.range_pattern[r][piece_c] = True
            # Diagonal lines (bishop)
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.range_pattern[r][c] = True
        
        elif preset_type == "knight":
            # Knight: L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2), 
                           (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.range_pattern[r][c] = True
        
        elif preset_type == "king":
            # King: 8 adjacent squares
            directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                         (0, 1), (1, -1), (1, 0), (1, 1)]
            for dr, dc in directions:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.range_pattern[r][c] = True
        
        elif preset_type == "global":
            # Global: Entire board except piece position
            for r in range(8):
                for c in range(8):
                    if [r, c] != self.range_piece_position:
                        self.range_pattern[r][c] = True
        
        # NOTE: Range data is automatically saved by CompleteDataManager
        self.update_range_preview()

    def select_range_preset(self, preset_type, button):
        """Select a range preset pattern and make it the active pattern"""
        # Clear other button selections
        for btn, _ in self.range_pattern_buttons:
            if btn != button:
                btn.setChecked(False)

        # Apply the pattern if button is checked
        if button.isChecked():
            self.apply_range_preset(preset_type)
            # Store the selected preset type
            self.selected_range_preset = preset_type
        else:
            # If unchecked, clear the pattern
            self.clear_range_selection()

    def clear_range_selection(self):
        """Clear range pattern and button selections"""
        # Clear all button selections
        for btn, _ in self.range_pattern_buttons:
            btn.setChecked(False)

        # Clear the pattern
        self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        self.range_piece_position = [4, 4]  # Reset to center
        self.selected_range_preset = None
        # NOTE: Range data is automatically saved by CompleteDataManager
        self.update_range_preview()

    # ========== PATTERN EDITORS ==========
    # All pattern editing functions for range, area effect, movement, etc.

    def update_range_preview(self):
        """Update the visual range preview (simplified since using external editor)"""
        # Since we're using the external range editor dialog,
        # we don't need to maintain an inline preview grid
        # The range data is stored in self.range_pattern and saved to ability data
        pass

    # ========== DATA MANAGEMENT ==========
    # All data handling functions for getting, setting, and managing ability data

    # get_ability_data() method removed - now using BaseEditor's standardized collect_widget_data()
    # This eliminates duplicate data collection patterns

    def set_ability_data(self, data):
        """Set form data from ability data using standardized BaseEditor approach"""
        try:
            # Check version and warn if outdated
            file_version = data.get('version', '0.0.0')
            current_version = '1.0.0'

            if file_version != current_version:
                self.status_widget.show_warning(f"⚠️ Outdated file format (v{file_version} → v{current_version}). Save to update.")

            # Use BaseEditor's standardized widget setting
            self.set_widget_values_from_data(data)

            # Update UI after data is loaded
            self.update_configuration_ui()

            self.status_label.setText("Ability data loaded and validated")

        except Exception as e:
            print(f"❌ Error setting ability data: {e}")
            self.status_label.setText("Error loading ability data")

    # ========== BASEEDITOR ABSTRACT METHOD IMPLEMENTATIONS ==========

    def reset_form(self):
        """Reset the form to default values (BaseEditor abstract method)"""
        # Create default ability structure
        default_ability = {
            "version": "1.0.0",
            "name": "",
            "description": "",
            "cost": 0,
            "activationMode": "auto",
            "tags": [],
            "rangeMask": [[False for _ in range(8)] for _ in range(8)],
            "piecePosition": [4, 4],
            "rangeFriendlyOnly": False,
            "rangeEnemyOnly": False
        }

        # Set default data
        self.current_data = default_ability
        self.current_ability = self.current_data  # Legacy compatibility

        # Reset pattern and position data
        if hasattr(self, 'range_pattern'):
            self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        if hasattr(self, 'range_piece_position'):
            self.range_piece_position = [4, 4]

        # Reset area effect data
        if hasattr(self, 'area_effect_target'):
            self.area_effect_target = [4, 4]
        if hasattr(self, 'area_effect_center'):
            self.area_effect_center = [4, 4]
        if hasattr(self, 'custom_area_pattern'):
            self.custom_area_pattern = None

        # Reset custom pattern data
        if hasattr(self, 'displace_custom_map'):
            self.displace_custom_map = [[False for _ in range(8)] for _ in range(8)]
        if hasattr(self, 'duplicate_positions_map'):
            self.duplicate_positions_map = [[False for _ in range(8)] for _ in range(8)]
        
        # Reset basic fields
        self.name_edit.clear()
        self.description_edit.clear()
        self.cost_spin.setValue(0)
        self.activation_combo.setCurrentIndex(0)

        if hasattr(self, 'auto_cost_check'):
            self.auto_cost_check.setChecked(False)
        
        # Reset tags
        for checkbox in self.tag_groups.values():
            checkbox.setChecked(False)
        
        # Reset all piece and ability lists
        list_attributes = [
            'summon_pieces', 'revival_pieces', 'carry_pieces', 'swap_pieces',
            'pass_through_pieces', 'adjacency_pieces', 'prevent_abilities', 'prevent_pieces',
            'displace_targets', 'immobilize_targets', 'convert_targets',
            'buff_targets', 'debuff_targets', 'reaction_targets', 'carry_targets', 'revival_targets',
            'buff_abilities', 'debuff_prevent_abilities'
        ]

        for attr in list_attributes:
            setattr(self, attr, [])

        # Clear list widgets (with null checks)
        list_widgets = [
            'summon_list', 'revival_list', 'carry_list', 'swap_list', 'pass_through_list',
            'adjacency_list', 'prevent_ability_list', 'prevent_piece_list',
            'displace_target_list', 'immobilize_target_list', 'convert_target_list',
            'buff_target_list', 'debuff_target_list', 'reaction_target_list',
            'carry_target_list', 'revival_target_list', 'buff_ability_list', 'debuff_prevent_ability_list'
        ]

        for widget_name in list_widgets:
            if hasattr(self, widget_name):
                widget = getattr(self, widget_name)
                if widget is not None:
                    try:
                        widget.clear()
                    except RuntimeError:
                        pass
        
        # Reset all editor dialog states to defaults (no-op in new system)

        # Update UI components
        self.update_configuration_ui()
        self.update_range_preview()
        self.status_label.setText("Ready")

    def post_load_update(self):
        """Update UI components after loading data (BaseEditor abstract method)"""
        # Update UI after data is loaded
        self.update_configuration_ui()
        self.status_widget.show_success("Ability loaded successfully")

    def post_save_update(self):
        """Update UI components after saving data (BaseEditor abstract method)"""
        # Refresh ability list dropdown
        self.refresh_ability_list()
        self.status_widget.show_success("Ability saved successfully")

    def save_as_data(self) -> bool:
        """Save data with new filename (BaseEditor abstract method)"""
        return self.save_as_ability()

    def refresh_file_lists(self):
        """Refresh file lists/dropdowns (BaseEditor abstract method)"""
        self.refresh_ability_list()
    


    # ========== FILE OPERATIONS ==========
    # All file-related operations for abilities

    def new_ability(self):
        """Create a new ability using standardized BaseEditor method"""
        self.new_data()  # Use BaseEditor's standardized new_data method
    
    def load_ability(self):
        """Load an ability from file using standardized BaseEditor method"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Load Ability", ABILITIES_DIR, "JSON Files (*.json)"
        )

        if filename:
            base_filename = os.path.basename(filename).replace('.json', '')
            self.load_data(base_filename)  # Use BaseEditor's standardized load_data method
    
    def validate_ability_data(self, data):
        """Validate ability data - PERMISSIVE MODE for maximum flexibility"""
        # Skip validation - allow saving any data
        return []
    
    def save_ability(self):
        """Save the current ability using standardized BaseEditor method"""
        return self.save_data()  # Use BaseEditor's standardized save_data method
    
    def save_as_ability(self) -> bool:
        """Save the ability with a new filename using standardized BaseEditor method"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Warning", "Please enter an ability name before saving.")
            return False

        suggested_name = self.name_edit.text().replace(" ", "_").lower() + ".json"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Ability As", os.path.join(ABILITIES_DIR, suggested_name), "JSON Files (*.json)"
        )

        if file_path:
            filename = os.path.basename(file_path).replace('.json', '')
            return self.save_data(filename)  # Use BaseEditor's standardized save_data method

        return False

    # ========== LIST MANAGEMENT (by ability tag) ==========
    # All list management functions organized by their corresponding ability tags

    # --- Summon ---
    # NOTE: Summon piece management is now handled by inline selectors
    # These methods are kept for compatibility but are no longer used

    def on_summon_pieces_changed(self):
        """Handle changes to summon pieces from inline selector"""
        if hasattr(self, 'summon_selector'):
            self.summon_pieces = self.summon_selector.get_pieces()
            print(f"Summon pieces updated: {len(self.summon_pieces)} pieces")
            # Trigger auto-cost recalculation if enabled
            if hasattr(self, 'auto_cost_check') and self.auto_cost_check.isChecked():
                self.calculate_auto_cost()

    def on_buff_targets_changed(self):
        """Handle changes to buff targets from inline selector"""
        if hasattr(self, 'buff_target_selector'):
            self.buff_targets = self.buff_target_selector.get_pieces()
            print(f"Buff targets updated: {len(self.buff_targets)} pieces")
            # Trigger auto-cost recalculation if enabled
            if hasattr(self, 'auto_cost_check') and self.auto_cost_check.isChecked():
                self.calculate_auto_cost()

    def on_buff_abilities_changed(self):
        """Handle changes to buff abilities from inline selector"""
        if hasattr(self, 'buff_ability_selector'):
            self.buff_abilities = self.buff_ability_selector.get_abilities()
            print(f"Buff abilities updated: {len(self.buff_abilities)} abilities")
            # Trigger auto-cost recalculation if enabled
            if hasattr(self, 'auto_cost_check') and self.auto_cost_check.isChecked():
                self.calculate_auto_cost()

    def on_swap_pieces_changed(self):
        """Handle changes to swap pieces from inline selector"""
        if hasattr(self, 'swap_selector'):
            self.swap_pieces = self.swap_selector.get_pieces()
            print(f"Swap pieces updated: {len(self.swap_pieces)} pieces")

    def on_adjacency_pieces_changed(self):
        """Handle changes to adjacency pieces from inline selector"""
        if hasattr(self, 'adjacency_selector'):
            self.adjacency_pieces = self.adjacency_selector.get_pieces()
            print(f"Adjacency pieces updated: {len(self.adjacency_pieces)} pieces")

    def on_displace_targets_changed(self):
        """Handle changes to displace targets from inline selector"""
        if hasattr(self, 'displace_target_selector'):
            self.displace_targets = self.displace_target_selector.get_pieces()
            print(f"Displace targets updated: {len(self.displace_targets)} pieces")

    def on_convert_targets_changed(self):
        """Handle changes to convert targets from inline selector"""
        if hasattr(self, 'convert_target_selector'):
            self.convert_targets = self.convert_target_selector.get_pieces()
            print(f"Convert targets updated: {len(self.convert_targets)} pieces")

    def on_pass_through_pieces_changed(self):
        """Handle changes to pass through pieces from inline selector"""
        if hasattr(self, 'pass_through_selector'):
            self.pass_through_pieces = self.pass_through_selector.get_pieces()
            print(f"Pass through pieces updated: {len(self.pass_through_pieces)} pieces")

    def on_immobilize_targets_changed(self):
        """Handle changes to immobilize targets from inline selector"""
        if hasattr(self, 'immobilize_target_selector'):
            self.immobilize_targets = self.immobilize_target_selector.get_pieces()
            print(f"Immobilize targets updated: {len(self.immobilize_targets)} pieces")

    def on_revival_targets_changed(self):
        """Handle changes to revival targets from inline selector"""
        if hasattr(self, 'revival_target_selector'):
            self.revival_targets = self.revival_target_selector.get_pieces()
            print(f"Revival targets updated: {len(self.revival_targets)} pieces")

    def on_carry_targets_changed(self):
        """Handle changes to carry targets from inline selector"""
        if hasattr(self, 'carry_target_selector'):
            self.carry_targets = self.carry_target_selector.get_pieces()
            print(f"Carry targets updated: {len(self.carry_targets)} pieces")

    def on_reaction_targets_changed(self):
        """Handle changes to reaction targets from inline selector"""
        if hasattr(self, 'reaction_target_selector'):
            self.reaction_targets = self.reaction_target_selector.get_pieces()
            print(f"Reaction targets updated: {len(self.reaction_targets)} pieces")

    def on_debuff_targets_changed(self):
        """Handle changes to debuff targets from inline selector"""
        if hasattr(self, 'debuff_target_selector'):
            self.debuff_targets = self.debuff_target_selector.get_pieces()
            print(f"Debuff targets updated: {len(self.debuff_targets)} pieces")

    def on_debuff_prevent_abilities_changed(self):
        """Handle changes to debuff prevent abilities from inline selector"""
        if hasattr(self, 'debuff_prevent_ability_selector'):
            self.debuff_prevent_abilities = self.debuff_prevent_ability_selector.get_abilities()
            print(f"Debuff prevent abilities updated: {len(self.debuff_prevent_abilities)} abilities")

    def update_inline_selectors_with_data(self):
        """Update all inline selectors with current data after they're created"""
        # Only update selectors if we have current ability data
        if not hasattr(self, 'current_ability') or not self.current_ability:
            return

        # Define all inline selector mappings (complete list)
        # NOTE: Using JSON-compatible field names to match saved files
        selector_mappings = [
            # (selector_attr, data_key_in_ability, is_ability)
            ('summon_selector', 'summonList', False),
            ('buff_target_selector', 'buffTargetList', False),
            ('buff_ability_selector', 'buffAbilities', True),
            ('swap_selector', 'swapList', False),
            ('adjacency_selector', 'adjacencyList', False),
            ('displace_target_selector', 'displaceTargetList', False),
            ('immobilize_target_selector', 'immobilizeTargetList', False),
            ('convert_target_selector', 'convertTargetList', False),
            ('revival_target_selector', 'revivalList', False),
            ('carry_target_selector', 'carryList', False),
            ('reaction_target_selector', 'reactionTargets', False),
            ('debuff_target_selector', 'debuffTargetList', False),
            ('debuff_prevent_ability_selector', 'debuffPreventAbilities', True),
            ('pass_through_selector', 'passThroughList', False),
        ]

        # Update each selector if it exists (load data even if empty)
        for selector_attr, data_key, is_ability in selector_mappings:
            if hasattr(self, selector_attr):
                selector = getattr(self, selector_attr)
                data_list = self.current_ability.get(data_key, [])

                if selector is not None:
                    try:
                        # Check if the widget is still valid before calling methods on it
                        if hasattr(selector, 'isVisible'):  # Basic Qt widget check
                            if is_ability:
                                selector.set_abilities(data_list)
                            else:
                                selector.set_pieces(data_list)
                            print(f"✓ Loaded {len(data_list)} items for {selector_attr} from {data_key}")
                    except RuntimeError as e:
                        # Widget has been deleted, ignore silently
                        if "wrapped C/C++ object" not in str(e):
                            print(f"Warning: Could not update {selector_attr}: {e}")
                    except Exception as e:
                        print(f"Warning: Could not update {selector_attr}: {e}")

        # Note: carry starting piece is now handled as a simple boolean checkbox

    # NOTE: List display updates are handled automatically by inline selectors
    # when items are added/removed through inline selector widgets

    # NOTE: Revival and carry methods are in their respective ability tag sections

    # NOTE: Old dialog methods for swap, pass through, and adjacency pieces have been removed
    # These configurations now use inline selectors with automatic data management
    
    # Methods for prevent ability management
    # NOTE: These are legacy methods - prevent abilities are now handled by inline selectors
    
    def get_piece_names(self):
        """Get list of available piece names from data/pieces folder"""
        try:
            names = ["Any"] + simple_bridge.list_pieces()
            return names
        except:
            return ["Any", "Pawn", "Knight", "Bishop", "Rook", "Queen", "King"]
    
    # Missing callback methods
    def refresh_ability_list(self):
        """Refresh the ability dropdown list"""
        self.load_combo.clear()
        try:
            ability_names = simple_bridge.list_abilities()
            for ability_name in ability_names:
                ability_data, error = simple_bridge.load_ability_for_ui(ability_name)
                if ability_data and not error:
                    self.load_combo.addItem(ability_data.get('name', ability_name))
        except Exception as e:
            print(f"Error refreshing ability list: {e}")
    

    def on_load_selection_changed(self, text):
        """Handle load combo selection change"""
        if text and text != "Search abilities...":
            # Find the filename for this display name
            for i in range(self.load_combo.count()):
                if self.load_combo.itemText(i) == text:
                    filename = self.load_combo.itemData(i)
                    if filename:
                        self.load_ability_by_filename(filename)
                    break
    
    def load_ability_by_filename(self, filename):
        """Load ability by filename"""
        try:
            ability_data, error = simple_bridge.load_ability_for_ui(filename)
            if error:
                QMessageBox.critical(self, "Error", f"Failed to load ability:\n{error}")
                return
            self.set_ability_data(ability_data)
            self.current_filename = filename
            ability_name = ability_data.get('name', 'Unknown')
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load ability:\n{str(e)}")
            return
        self.status_label.setText(f"Loaded ability: {ability_name}")

    # ========== EVENT HANDLERS ==========
    # All event handling functions for UI interactions

    def on_auto_cost_changed(self, state):
        """Handle auto cost checkbox change"""
        if state == Qt.CheckState.Checked.value:
            self.cost_spin.setEnabled(False)
            self.calculate_auto_cost()
            print("✓ Auto-cost enabled: Logic engine will determine costs from individual ability/target complexity")
        else:
            self.cost_spin.setEnabled(True)
            # Clear individual costs when disabled
            if hasattr(self, 'individual_costs'):
                self.individual_costs = []
            print("✓ Auto-cost disabled: Manual cost setting enabled")
    
    def calculate_auto_cost(self):
        """Calculate and display individual costs from ability complexity"""
        individual_costs = []

        # Collect costs from all inline selectors
        cost_sources = [
            ('summon_pieces', 'summon_selector', 'Summon'),
            ('buff_targets', 'buff_target_selector', 'Buff Targets'),
            ('buff_abilities', 'buff_ability_selector', 'Buff Abilities'),
            ('swap_pieces', 'swap_selector', 'Swap'),
            ('adjacency_pieces', 'adjacency_selector', 'Adjacency'),
            ('displace_targets', 'displace_target_selector', 'Displace'),
            ('immobilize_targets', 'immobilize_target_selector', 'Immobilize'),
            ('convert_targets', 'convert_target_selector', 'Convert'),
            ('revival_targets', 'revival_target_selector', 'Revival'),
            ('carry_targets', 'carry_target_selector', 'Carry'),
            ('reaction_targets', 'reaction_target_selector', 'Reaction'),
            ('debuff_targets', 'debuff_target_selector', 'Debuff Targets'),
            ('debuff_prevent_abilities', 'debuff_prevent_ability_selector', 'Debuff Prevent'),
            ('pass_through_pieces', 'pass_through_selector', 'Pass Through'),
        ]

        # Collect individual costs from each configuration
        for data_attr, selector_attr, display_name in cost_sources:
            if hasattr(self, data_attr):
                data_list = getattr(self, data_attr, [])
                for item in data_list:
                    cost = item.get('cost', 0)
                    if cost > 0:
                        item_name = item.get('piece', item.get('ability', 'Unknown'))
                        individual_costs.append(f"{display_name}: {item_name} ({cost})")

        # Additional costs from configuration complexity
        if hasattr(self, 'area_size_spin') and self.area_size_spin is not None:
            try:
                area_cost = max(0, self.area_size_spin.value() - 1)
                if area_cost > 0:
                    individual_costs.append(f"Area Effect: Size {self.area_size_spin.value()} ({area_cost})")
            except (RuntimeError, AttributeError):
                pass

        if hasattr(self, 'range_pattern'):
            try:
                range_tiles = sum(sum(row) for row in self.range_pattern)
                range_cost = max(0, range_tiles // 8)
                if range_cost > 0:
                    individual_costs.append(f"Range Pattern: {range_tiles} tiles ({range_cost})")
            except (AttributeError, TypeError):
                pass

        # Display individual costs or calculate total
        if individual_costs:
            # Show individual costs in a readable format
            cost_display = "; ".join(individual_costs)
            total_cost = sum(int(cost.split('(')[-1].split(')')[0]) for cost in individual_costs)

            # Update cost field to show individual breakdown
            if hasattr(self, 'cost_spin'):
                self.cost_spin.setValue(max(1, total_cost))
                # Store the individual costs for JSON export
                self.individual_costs = individual_costs
                print(f"Auto-calculated costs: {cost_display} | Total: {total_cost}")
        else:
            # No individual costs found, use base cost
            if hasattr(self, 'cost_spin'):
                self.cost_spin.setValue(1)
                self.individual_costs = []
    
    def on_carry_drop_death_changed(self, state):
        """Handle carry drop on death checkbox change"""
        if hasattr(self, 'carry_drop_group'):
            self.carry_drop_group.setVisible(state == Qt.CheckState.Checked.value)
    
    def on_carry_drop_mode_changed(self):
        """Handle carry drop mode radio button changes"""
        if hasattr(self, 'carry_drop_range_spin'):
            show_range = (self.carry_drop_random_radio.isChecked() or 
                         self.carry_drop_selectable_radio.isChecked())
            self.carry_drop_range_spin.setVisible(show_range)
    
    def on_range_side_changed(self, state):
        """Handle range side checkbox changes (make mutually exclusive)"""
        sender = self.sender()
        if state == Qt.CheckState.Checked.value:
            if sender == self.range_friendly_only_check:
                self.range_enemy_only_check.setChecked(False)
            elif sender == self.range_enemy_only_check:
                self.range_friendly_only_check.setChecked(False)
    
    def update_area_effect_preview(self):
        """Update the area effect visual preview"""
        if not hasattr(self, 'area_effect_grid') or not self.area_effect_grid:
            return
        
        if not hasattr(self, 'area_size_spin') or self.area_size_spin is None:
            return
        
        if not hasattr(self, 'area_shape_combo') or self.area_shape_combo is None:
            return
        
        size = self.area_size_spin.value()  # 1=1x1, 2=2x2, etc.
        shape = self.area_shape_combo.currentText()
        
        # Get target and effect center positions
        if not hasattr(self, 'area_effect_target'):
            self.area_effect_target = [4, 4]  # Default center
        if not hasattr(self, 'area_effect_center'):
            self.area_effect_center = [4, 4]  # Default center
        
        target_r, target_c = self.area_effect_target
        center_r, center_c = self.area_effect_center
        
        # Clear all squares first
        for r in range(8):
            for c in range(8):
                btn = self.area_effect_grid[r][c]
                is_light = (r + c) % 2 == 0
                if is_light:
                    btn.setStyleSheet("background: #f0d9b5; border: 1px solid #b58863;")
                else:
                    btn.setStyleSheet("background: #b58863; border: 1px solid #8b4513;")
                btn.setText("")
        
        # Handle custom pattern
        if shape == "Custom":
            if hasattr(self, 'custom_area_pattern'):
                for r in range(8):
                    for c in range(8):
                        if r < len(self.custom_area_pattern) and c < len(self.custom_area_pattern[r]):
                            if self.custom_area_pattern[r][c]:
                                btn = self.area_effect_grid[r][c]
                                if r == target_r and c == target_c:
                                    # Target square in effect area - special color
                                    btn.setStyleSheet("background: #ff9900; border: 2px solid #cc6600; font-weight: bold; color: white;")
                                    btn.setText("🎯")
                                else:
                                    btn.setStyleSheet("background: #ffcc66; border: 2px solid #cc8800; font-weight: bold;")
                                    btn.setText("💥")
            # Add target marker if not in effect area
            if not (hasattr(self, 'custom_area_pattern') and 
                   target_r < len(self.custom_area_pattern) and 
                   target_c < len(self.custom_area_pattern[target_r]) and 
                   self.custom_area_pattern[target_r][target_c]):
                target_btn = self.area_effect_grid[target_r][target_c]
                target_btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold; color: white;")
                target_btn.setText("🎯")
            return
        
        # Calculate affected squares based on size and shape
        affected_squares = set()
        
        if shape == "Square":
            # Square pattern: size x size area centered on effect center
            half_size = size // 2
            for r in range(max(0, center_r - half_size), min(8, center_r + size - half_size)):
                for c in range(max(0, center_c - half_size), min(8, center_c + size - half_size)):
                    affected_squares.add((r, c))
        
        elif shape == "Circle":
            # Circle pattern: try to fit within size x size bounds centered on effect center
            radius = size // 2
            for r in range(8):
                for c in range(8):
                    distance = ((r - center_r) ** 2 + (c - center_c) ** 2) ** 0.5
                    if distance <= radius + 0.5:  # Add 0.5 for better circle approximation
                        affected_squares.add((r, c))
        
        elif shape == "Cross":
            # Cross pattern: + shape within size bounds centered on effect center
            half_size = size // 2
            # Horizontal line
            for c in range(max(0, center_c - half_size), min(8, center_c + half_size + 1)):
                affected_squares.add((center_r, c))
            # Vertical line
            for r in range(max(0, center_r - half_size), min(8, center_r + half_size + 1)):
                affected_squares.add((r, center_c))
        
        elif shape == "Line":
            # Horizontal line of specified size centered on effect center
            half_size = size // 2
            for c in range(max(0, center_c - half_size), min(8, center_c + size - half_size)):
                affected_squares.add((center_r, c))
        
        # Apply the effect visualization
        for r, c in affected_squares:
            if 0 <= r < 8 and 0 <= c < 8:
                btn = self.area_effect_grid[r][c]
                if r == target_r and c == target_c and r == center_r and c == center_c:
                    # Target and effect center are same - combined symbol
                    btn.setStyleSheet("background: #ff9900; border: 2px solid #cc6600; font-weight: bold; color: white;")
                    btn.setText("🎯")
                elif r == target_r and c == target_c:
                    # Target square in effect area
                    btn.setStyleSheet("background: #ff6600; border: 2px solid #cc3300; font-weight: bold; color: white;")
                    btn.setText("🎯")
                elif r == center_r and c == center_c:
                    # Effect center
                    btn.setStyleSheet("background: #cc9900; border: 2px solid #996600; font-weight: bold; color: white;")
                    btn.setText("⊕")
                else:
                    # Regular effect square
                    btn.setStyleSheet("background: #ffcc66; border: 2px solid #cc8800; font-weight: bold;")
                    btn.setText("💥")
        
        # Show target if not in effect area
        if (target_r, target_c) not in affected_squares:
            target_btn = self.area_effect_grid[target_r][target_c]
            target_btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold; color: white;")
            target_btn.setText("🎯")
        
        # Show effect center if not in effect area and different from target
        if ((center_r, center_c) not in affected_squares and 
            (center_r != target_r or center_c != target_c)):
            center_btn = self.area_effect_grid[center_r][center_c]
            center_btn.setStyleSheet("background: #996633; border: 2px solid #663300; font-weight: bold; color: white;")
            center_btn.setText("⊕")
    
    def on_area_shape_changed(self, shape):
        """Handle area shape change"""
        if hasattr(self, 'custom_area_btn'):
            self.custom_area_btn.setVisible(shape == "Custom")
        if hasattr(self, 'area_size_spin'):
            self.area_size_spin.setEnabled(shape != "Custom")
    
    def handle_area_effect_click(self, pos, row, col):
        """Handle right-click on area effect grid"""
        # pos parameter available for future use (click position)
        from PyQt6.QtWidgets import QApplication
        modifiers = QApplication.keyboardModifiers()
        
        if modifiers & Qt.KeyboardModifier.ShiftModifier:
            # Shift+Right-click: Move effect center
            self.area_effect_center = [row, col]
        else:
            # Right-click: Move target
            self.area_effect_target = [row, col]
        
        self.update_area_effect_preview()
    
    def set_area_effect_target(self, row, col):
        """Set the target position for area effect (right-click)"""
        self.area_effect_target = [row, col]
        self.update_area_effect_preview()
    
    def edit_custom_area_effect(self):
        """Open custom area effect editor"""
        initial_pattern = getattr(self, 'custom_area_pattern', None)
        initial_target = getattr(self, 'area_effect_target', [4, 4])
        
        dialog = AreaEffectMaskDialog(self, initial_pattern)
        dialog.set_target_pos(initial_target)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.custom_area_pattern = dialog.get_mask()
            self.area_effect_target = dialog.get_target_pos()
            self.update_area_effect_preview()
    
    def update_adjacency_grid_size(self):
        """Update the adjacency grid size based on distance"""
        if not hasattr(self, 'adjacency_grid_layout'):
            return
        
        # Clear existing grid
        for i in reversed(range(self.adjacency_grid_layout.count())): 
            self.adjacency_grid_layout.itemAt(i).widget().setParent(None)
        
        self.adjacency_grid = []
        distance = self.adjacency_distance_spin.value()
        grid_size = 2 * distance + 1  # e.g., distance 1 = 3x3, distance 2 = 5x5
        
        for r in range(grid_size):
            row = []
            for c in range(grid_size):
                btn = QPushButton()
                btn.setFixedSize(20, 20)
                btn.setCheckable(True)
                
                # Center piece (always checked and disabled)
                center = distance
                if r == center and c == center:
                    btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold; color: white;")
                    btn.setText("♔")
                    btn.setEnabled(False)
                else:
                    # Make clickable for adjacency selection
                    btn.clicked.connect(lambda checked, row=r, col=c: self.toggle_adjacency_tile(row, col, checked))
                    
                    # Check if this position was previously selected
                    relative_pos = (r - center, c - center)
                    if not hasattr(self, 'adjacency_selected'):
                        self.adjacency_selected = set()
                    
                    if relative_pos in self.adjacency_selected:
                        btn.setChecked(True)
                    
                    self.update_adjacency_tile_style(btn, r, c, center)
                
                self.adjacency_grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.adjacency_grid.append(row)
    
    def update_adjacency_preview(self):
        """Update adjacency preview when distance changes"""
        self.update_adjacency_grid_size()
    
    def toggle_adjacency_tile(self, row, col, checked):
        """Toggle adjacency tile selection"""
        if not hasattr(self, 'adjacency_selected'):
            self.adjacency_selected = set()
        
        distance = self.adjacency_distance_spin.value()
        center = distance
        relative_pos = (row - center, col - center)
        
        if checked:
            self.adjacency_selected.add(relative_pos)
        else:
            self.adjacency_selected.discard(relative_pos)
        
        # Update the tile style
        btn = self.adjacency_grid[row][col]
        self.update_adjacency_tile_style(btn, row, col, center)
    
    def update_adjacency_tile_style(self, btn, row, col, center):
        """Update the style of an adjacency tile"""
        is_light = (row + col) % 2 == 0
        relative_pos = (row - center, col - center)
        
        if not hasattr(self, 'adjacency_selected'):
            self.adjacency_selected = set()
        
        if relative_pos in self.adjacency_selected:
            # Selected tile - darker background with border
            btn.setStyleSheet("background: #66cc66; border: 3px solid #339933; font-weight: bold;")
            btn.setText("✓")
        else:
            # Normal chess board pattern
            if is_light:
                btn.setStyleSheet("background: #f0d9b5; border: 1px solid #b58863;")
            else:
                btn.setStyleSheet("background: #b58863; border: 1px solid #8b4513;")
            btn.setText("")
    
    def add_prevent_piece(self):
        """Add a piece to prevent list - handled by inline selector"""
        # This functionality is now handled by the inline piece selector
        pass

    def remove_prevent_piece(self):
        """Remove selected piece from prevent list - handled by inline selector"""
        # This functionality is now handled by the inline piece selector
        pass
    
    # New ability tag helper methods
    def on_displace_custom_changed(self, state):
        """Handle custom displacement map checkbox change"""
        if hasattr(self, 'displace_custom_btn'):
            self.displace_custom_btn.setVisible(state == Qt.CheckState.Checked.value)
        
        # Enable/disable direction and distance controls
        if hasattr(self, 'displace_direction_combo'):
            self.displace_direction_combo.setEnabled(state != Qt.CheckState.Checked.value)
        if hasattr(self, 'displace_distance_spin'):
            self.displace_distance_spin.setEnabled(state != Qt.CheckState.Checked.value)
    
    def edit_custom_displacement_map(self):
        """Open custom displacement map editor using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'displace_custom_map', None)
        initial_position = getattr(self, 'displace_piece_position', [3, 3])

        pattern, piece_position, _ = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Custom Displacement Pattern", self
        )

        if pattern is not None:
            self.displace_custom_map = pattern
            self.displace_piece_position = piece_position
            QMessageBox.information(self, "Pattern Saved", "Custom displacement pattern has been updated.")
    
    def on_pulse_enable_changed(self, state):
        """Handle pulse effect enable checkbox change"""
        if hasattr(self, 'pulse_interval_spin'):
            self.pulse_interval_spin.setEnabled(state == Qt.CheckState.Checked.value)
    
    def on_duplicate_limit_changed(self, state):
        """Handle duplicate limit checkbox change"""
        if hasattr(self, 'duplicate_limit_spin'):
            self.duplicate_limit_spin.setEnabled(state == Qt.CheckState.Checked.value)
    
    def edit_duplicate_positions(self):
        """Open duplicate positions editor using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'duplicate_positions_map', None)
        initial_position = getattr(self, 'duplicate_piece_position', [3, 3])

        pattern, piece_position, _ = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Duplicate Positions", self
        )

        if pattern is not None:
            self.duplicate_positions_map = pattern
            self.duplicate_piece_position = piece_position
            QMessageBox.information(self, "Pattern Saved", "Duplicate positions pattern has been updated.")
    
    # NOTE: Old dialog methods for displace, immobilize, and convert targets have been removed
    # These configurations now use inline selectors with automatic data management
    
    # ========== NEW ADVANCED ABILITY CONFIGURATION METHODS ==========
    def add_reaction_config(self):
        """Add reaction configuration section"""
        group = QGroupBox("⚡ Reaction Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()

        layout.addWidget(QLabel("Trigger this ability in response to specific events."))

        # Target piece selection (replacing dropdown with piece selector)
        target_group = QGroupBox("Reaction Targets")
        target_layout = QVBoxLayout()

        target_info = QLabel("Select which pieces can trigger this reaction:")
        target_info.setStyleSheet("color: #666; font-style: italic;")
        target_layout.addWidget(target_info)

        # Enhanced inline piece selector for reaction targets
        from dialogs.inline_selectors import InlinePieceSelector
        self.reaction_target_selector = InlinePieceSelector(self, "Target Pieces", allow_costs=True)
        self.reaction_target_selector.pieces_changed.connect(self.on_reaction_targets_changed)
        target_layout.addWidget(self.reaction_target_selector)
        target_group.setLayout(target_layout)
        layout.addWidget(target_group)

        # Event type checkboxes (replacing dropdown)
        events_group = QGroupBox("Trigger Events")
        events_layout = QVBoxLayout()

        events_info = QLabel("Select which events trigger this reaction:")
        events_info.setStyleSheet("color: #666; font-style: italic;")
        events_layout.addWidget(events_info)

        # Create checkboxes for each event type
        self.reaction_move_check = QCheckBox("Move - When target piece moves")
        self.reaction_capture_check = QCheckBox("Capture - When target piece captures or is captured")
        self.reaction_death_check = QCheckBox("Death - When target piece dies")
        self.reaction_enter_range_check = QCheckBox("Enter Range - When target piece enters this piece's range")
        self.reaction_exit_range_check = QCheckBox("Exit Range - When target piece exits this piece's range")
        self.reaction_turn_start_check = QCheckBox("Turn Start - At the start of each turn")
        self.reaction_turn_end_check = QCheckBox("Turn End - At the end of each turn")

        events_layout.addWidget(self.reaction_move_check)
        events_layout.addWidget(self.reaction_capture_check)
        events_layout.addWidget(self.reaction_death_check)
        events_layout.addWidget(self.reaction_enter_range_check)
        events_layout.addWidget(self.reaction_exit_range_check)
        events_layout.addWidget(self.reaction_turn_start_check)
        events_layout.addWidget(self.reaction_turn_end_check)

        events_group.setLayout(events_layout)
        layout.addWidget(events_group)

        # Uses action checkbox
        form_layout = QFormLayout()
        self.reaction_uses_action_check = QCheckBox("Counts as turn/action")
        self.reaction_uses_action_check.setToolTip("Whether using this reaction consumes the piece's action for the turn")
        form_layout.addRow("Action Cost:", self.reaction_uses_action_check)

        layout.addLayout(form_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)

        # Initialize reaction targets list
        if not hasattr(self, 'reaction_targets'):
            self.reaction_targets = []
        # NOTE: List display updates are handled automatically by DialogDataManager
    
    def add_buff_piece_config(self):
        """Add buff piece configuration section"""
        group = QGroupBox("⬆️ Buff Piece Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Enhance target pieces with temporary abilities or stat boosts."))
        
        # Enhanced inline target piece selector
        from dialogs.inline_selectors import InlinePieceSelector
        self.buff_target_selector = InlinePieceSelector(self, "Target Pieces", allow_costs=True)
        self.buff_target_selector.pieces_changed.connect(self.on_buff_targets_changed)
        layout.addWidget(self.buff_target_selector)
        
        # Duration - Updated default per glossary V1.0.1
        duration_layout = QFormLayout()
        self.buff_duration_spin = QSpinBox()
        self.buff_duration_spin.setRange(0, 20)
        self.buff_duration_spin.setValue(1)  # Default should be 1 per glossary V1.0.1
        self.buff_duration_spin.setSuffix(" turns")
        self.buff_duration_spin.setSpecialValueText("Indefinite")
        self.buff_duration_spin.setToolTip("How long the buff lasts (0 = indefinite)")
        duration_layout.addRow("Duration:", self.buff_duration_spin)
        layout.addLayout(duration_layout)
        
        # Buff Effects (toggleable sections)
        effects_group = QGroupBox("Buff Effects")
        effects_layout = QVBoxLayout()
        
        # Add Ability toggle
        self.buff_add_ability_check = QCheckBox("Add Temporary Abilities")
        self.buff_add_ability_check.stateChanged.connect(self.on_buff_add_ability_changed)
        effects_layout.addWidget(self.buff_add_ability_check)
        
        # Enhanced inline buff abilities selector
        from dialogs.inline_selectors import InlineAbilitySelector
        self.buff_ability_selector = InlineAbilitySelector(self, "Buff Abilities")
        self.buff_ability_selector.abilities_changed.connect(self.on_buff_abilities_changed)
        self.buff_ability_selector.setVisible(False)
        effects_layout.addWidget(self.buff_ability_selector)
        
        # Movement and Attack Pattern (consolidated)
        self.buff_movement_pattern_check = QCheckBox("Modify Movement & Attack Pattern")
        self.buff_movement_pattern_check.stateChanged.connect(self.on_buff_movement_pattern_changed)
        effects_layout.addWidget(self.buff_movement_pattern_check)

        self.buff_movement_pattern_btn = QPushButton("Edit Movement & Attack Pattern")
        self.buff_movement_pattern_btn.clicked.connect(self.edit_buff_movement_pattern)
        self.buff_movement_pattern_btn.setVisible(False)
        effects_layout.addWidget(self.buff_movement_pattern_btn)
        
        effects_group.setLayout(effects_layout)
        layout.addWidget(effects_group)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize buff targets list
        if not hasattr(self, 'buff_targets'):
            self.buff_targets = []
        if not hasattr(self, 'buff_abilities'):
            self.buff_abilities = []
        if not hasattr(self, 'buff_movement_pattern'):
            self.buff_movement_pattern = [[False for _ in range(8)] for _ in range(8)]
        
        # NOTE: List display updates are handled automatically by inline selectors
    
    def add_debuff_piece_config(self):
        """Add debuff piece configuration section"""
        group = QGroupBox("⬇️ Debuff Piece Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Weaken target pieces with negative effects or ability blocks."))
        
        # Enhanced inline piece selector for debuff targets
        from dialogs.inline_selectors import InlinePieceSelector
        self.debuff_target_selector = InlinePieceSelector(self, "Target Pieces", allow_costs=True)
        self.debuff_target_selector.pieces_changed.connect(self.on_debuff_targets_changed)
        layout.addWidget(self.debuff_target_selector)
        
        # Duration - Updated default per glossary V1.0.1
        duration_layout = QFormLayout()
        self.debuff_duration_spin = QSpinBox()
        self.debuff_duration_spin.setRange(0, 20)
        self.debuff_duration_spin.setValue(1)  # Default should be 1 per glossary V1.0.1
        self.debuff_duration_spin.setSuffix(" turns")
        self.debuff_duration_spin.setSpecialValueText("Indefinite")
        self.debuff_duration_spin.setToolTip("How long the debuff lasts (0 = indefinite)")
        duration_layout.addRow("Duration:", self.debuff_duration_spin)
        layout.addLayout(duration_layout)
        
        # Debuff Effects
        effects_group = QGroupBox("Debuff Effects")
        effects_layout = QVBoxLayout()
        
        # Prevent Ability toggle
        self.debuff_prevent_ability_check = QCheckBox("Prevent Abilities")
        self.debuff_prevent_ability_check.stateChanged.connect(self.on_debuff_prevent_ability_changed)
        effects_layout.addWidget(self.debuff_prevent_ability_check)

        # Enhanced inline ability selector for prevent abilities
        from dialogs.inline_selectors import InlineAbilitySelector
        self.debuff_prevent_ability_selector = InlineAbilitySelector(self, "Prevent Abilities")
        self.debuff_prevent_ability_selector.abilities_changed.connect(self.on_debuff_prevent_abilities_changed)
        self.debuff_prevent_ability_selector.setVisible(False)
        effects_layout.addWidget(self.debuff_prevent_ability_selector)
        
        # Line of Sight Prevention (moved from preventAbility tag)
        self.debuff_prevent_los_check = QCheckBox("Prevent Line of Sight")
        self.debuff_prevent_los_check.setToolTip("Prevents target from using abilities that require line of sight")
        effects_layout.addWidget(self.debuff_prevent_los_check)
        
        # Movement and Attack Pattern (consolidated)
        self.debuff_movement_pattern_check = QCheckBox("Modify Movement & Attack Pattern")
        self.debuff_movement_pattern_check.stateChanged.connect(self.on_debuff_movement_pattern_changed)
        effects_layout.addWidget(self.debuff_movement_pattern_check)

        self.debuff_movement_pattern_btn = QPushButton("Edit Movement & Attack Pattern")
        self.debuff_movement_pattern_btn.clicked.connect(self.edit_debuff_movement_pattern)
        self.debuff_movement_pattern_btn.setVisible(False)
        effects_layout.addWidget(self.debuff_movement_pattern_btn)
        
        effects_group.setLayout(effects_layout)
        layout.addWidget(effects_group)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize debuff data
        if not hasattr(self, 'debuff_targets'):
            self.debuff_targets = []
        if not hasattr(self, 'debuff_prevent_abilities'):
            self.debuff_prevent_abilities = []
        if not hasattr(self, 'debuff_movement_pattern'):
            self.debuff_movement_pattern = [[False for _ in range(8)] for _ in range(8)]
        
        # NOTE: List display updates are handled automatically by DialogDataManager
    
    def add_invisible_config(self):
        """Add invisible configuration section"""
        group = QGroupBox("👻 Invisible Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Makes piece invisible until certain conditions are met."))
        
        # Invisibility Settings
        settings_group = QGroupBox("Reveal Conditions")
        settings_layout = QVBoxLayout()
        
        # Reveal on Move
        move_layout = QHBoxLayout()
        self.invisible_reveal_move_check = QCheckBox("Reveal after Move")
        move_layout.addWidget(self.invisible_reveal_move_check)
        self.invisible_reveal_move_spin = QSpinBox()
        self.invisible_reveal_move_spin.setRange(1, 10)
        self.invisible_reveal_move_spin.setValue(1)
        self.invisible_reveal_move_spin.setEnabled(False)
        move_layout.addWidget(self.invisible_reveal_move_spin)
        move_layout.addWidget(QLabel("moves"))
        move_layout.addStretch()
        settings_layout.addLayout(move_layout)
        
        self.invisible_reveal_move_check.stateChanged.connect(
            lambda state: self.invisible_reveal_move_spin.setEnabled(state == Qt.CheckState.Checked.value)
        )
        
        # Reveal on Capture
        capture_layout = QHBoxLayout()
        self.invisible_reveal_capture_check = QCheckBox("Reveal after Capture")
        capture_layout.addWidget(self.invisible_reveal_capture_check)
        self.invisible_reveal_capture_spin = QSpinBox()
        self.invisible_reveal_capture_spin.setRange(1, 10)
        self.invisible_reveal_capture_spin.setValue(1)
        self.invisible_reveal_capture_spin.setEnabled(False)
        capture_layout.addWidget(self.invisible_reveal_capture_spin)
        capture_layout.addWidget(QLabel("captures"))
        capture_layout.addStretch()
        settings_layout.addLayout(capture_layout)
        
        self.invisible_reveal_capture_check.stateChanged.connect(
            lambda state: self.invisible_reveal_capture_spin.setEnabled(state == Qt.CheckState.Checked.value)
        )
        
        # Reveal on Action
        action_layout = QHBoxLayout()
        self.invisible_reveal_action_check = QCheckBox("Reveal after Action")
        action_layout.addWidget(self.invisible_reveal_action_check)
        self.invisible_reveal_action_spin = QSpinBox()
        self.invisible_reveal_action_spin.setRange(1, 10)
        self.invisible_reveal_action_spin.setValue(1)
        self.invisible_reveal_action_spin.setEnabled(False)
        action_layout.addWidget(self.invisible_reveal_action_spin)
        action_layout.addWidget(QLabel("actions"))
        action_layout.addStretch()
        settings_layout.addLayout(action_layout)
        
        self.invisible_reveal_action_check.stateChanged.connect(
            lambda state: self.invisible_reveal_action_spin.setEnabled(state == Qt.CheckState.Checked.value)
        )
        
        # Reveal on Enemy LoS
        self.invisible_reveal_los_check = QCheckBox("Reveal on Enemy Line of Sight")
        self.invisible_reveal_los_check.setToolTip("Automatically reveal if an enemy piece can see this piece")
        settings_layout.addWidget(self.invisible_reveal_los_check)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    def add_trap_tile_config(self):
        """Add trap tile configuration section"""
        group = QGroupBox("🪤 Trap Tile Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Create traps on target tiles with various effects."))
        
        # Trap Effects (expandable toggles)
        effects_group = QGroupBox("Trap Effects")
        effects_layout = QVBoxLayout()
        
        # Capture effect
        self.trap_capture_check = QCheckBox("Capture (Destroys target)")
        effects_layout.addWidget(self.trap_capture_check)
        
        # Immobilize effect
        immobilize_layout = QHBoxLayout()
        self.trap_immobilize_check = QCheckBox("Immobilize for")
        immobilize_layout.addWidget(self.trap_immobilize_check)
        self.trap_immobilize_spin = QSpinBox()
        self.trap_immobilize_spin.setRange(0, 10)
        self.trap_immobilize_spin.setValue(2)
        self.trap_immobilize_spin.setSuffix(" turns")
        self.trap_immobilize_spin.setSpecialValueText("Indefinite")
        self.trap_immobilize_spin.setToolTip("Duration of immobilization (0 = indefinite)")
        self.trap_immobilize_spin.setEnabled(False)
        immobilize_layout.addWidget(self.trap_immobilize_spin)
        immobilize_layout.addStretch()
        effects_layout.addLayout(immobilize_layout)
        
        self.trap_immobilize_check.stateChanged.connect(
            lambda state: self.trap_immobilize_spin.setEnabled(state == Qt.CheckState.Checked.value)
        )
        
        # Teleport effect
        teleport_layout = QHBoxLayout()
        self.trap_teleport_check = QCheckBox("Teleport")
        teleport_layout.addWidget(self.trap_teleport_check)
        self.trap_teleport_btn = QPushButton("Edit Teleport Range")
        self.trap_teleport_btn.clicked.connect(self.edit_trap_teleport_range)
        self.trap_teleport_btn.setEnabled(False)
        teleport_layout.addWidget(self.trap_teleport_btn)
        teleport_layout.addStretch()
        effects_layout.addLayout(teleport_layout)
        
        self.trap_teleport_check.stateChanged.connect(
            lambda state: self.trap_teleport_btn.setEnabled(state == Qt.CheckState.Checked.value)
        )
        
        # Add Ability effect
        ability_layout = QHBoxLayout()
        self.trap_add_ability_check = QCheckBox("Add Embedded Effect")
        ability_layout.addWidget(self.trap_add_ability_check)
        self.trap_ability_btn = QPushButton("Select Ability")
        self.trap_ability_btn.clicked.connect(self.select_trap_ability)
        self.trap_ability_btn.setEnabled(False)
        ability_layout.addWidget(self.trap_ability_btn)
        self.trap_ability_label = QLabel("None selected")
        self.trap_ability_label.setStyleSheet("color: #666; font-style: italic;")
        ability_layout.addWidget(self.trap_ability_label)
        ability_layout.addStretch()
        effects_layout.addLayout(ability_layout)
        
        self.trap_add_ability_check.stateChanged.connect(
            lambda state: self.trap_ability_btn.setEnabled(state == Qt.CheckState.Checked.value)
        )
        
        effects_group.setLayout(effects_layout)
        layout.addWidget(effects_group)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize trap teleport range
        if not hasattr(self, 'trap_teleport_range'):
            self.trap_teleport_range = [[False for _ in range(8)] for _ in range(8)]
    
    def add_requires_starting_position_config(self):
        """Add requires starting position configuration section"""
        group = QGroupBox("🏠 Starting Position Requirement")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        info_label = QLabel("This ability can only be activated if the piece is still in its starting position.")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(info_label)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    # ========== ENHANCED EXISTING CONFIGURATIONS ==========
    
    def add_revival_config(self):
        """Revival configuration with enhanced options"""
        group = QGroupBox("⚰️ Revival Configuration")
        layout = QVBoxLayout()
        
        # Basic revival description
        layout.addWidget(QLabel("Revive pieces that have been captured or destroyed."))
        
        # Enhanced inline piece selector for revival targets
        from dialogs.inline_selectors import InlinePieceSelector
        self.revival_target_selector = InlinePieceSelector(self, "Revival Targets", allow_costs=True)
        self.revival_target_selector.pieces_changed.connect(self.on_revival_targets_changed)
        layout.addWidget(self.revival_target_selector)
        
        # Enhanced revival options
        options_layout = QFormLayout()

        # Max pieces per turn (missing spinner added)
        self.revival_max_pieces_spin = QSpinBox()
        self.revival_max_pieces_spin.setRange(1, 10)
        self.revival_max_pieces_spin.setValue(1)
        self.revival_max_pieces_spin.setToolTip("Maximum pieces that can be revived per turn")
        options_layout.addRow("Max Pieces per Turn:", self.revival_max_pieces_spin)

        # Sacrifice mode
        self.revival_sacrifice_check = QCheckBox("Sacrifice Mode")
        self.revival_sacrifice_check.setToolTip("Use sacrifice instead of points for revival")
        self.revival_sacrifice_check.stateChanged.connect(self.on_revival_sacrifice_changed)
        options_layout.addRow("Sacrifice:", self.revival_sacrifice_check)

        # Max cost (indented - only available when sacrifice mode is checked)
        max_cost_layout = QHBoxLayout()
        max_cost_layout.addWidget(QLabel("    "))  # Indentation
        max_cost_layout.addWidget(QLabel("Max Cost:"))
        self.revival_max_cost_spin = QSpinBox()
        self.revival_max_cost_spin.setRange(1, 10)
        self.revival_max_cost_spin.setValue(3)
        self.revival_max_cost_spin.setToolTip("Maximum points to offer on death")
        self.revival_max_cost_spin.setEnabled(False)
        max_cost_layout.addWidget(self.revival_max_cost_spin)
        max_cost_layout.addStretch()

        max_cost_widget = QWidget()
        max_cost_widget.setLayout(max_cost_layout)
        options_layout.addRow("", max_cost_widget)
        
        # Revive with points
        self.revival_with_points_check = QCheckBox("Revive with Points")
        self.revival_with_points_check.setToolTip("Enable point-based revival")
        self.revival_with_points_check.stateChanged.connect(self.on_revival_points_changed)
        options_layout.addRow("Points:", self.revival_with_points_check)

        # Points amount (indented - only available when revive with points is checked)
        points_layout = QHBoxLayout()
        points_layout.addWidget(QLabel("    "))  # Indentation
        points_layout.addWidget(QLabel("Amount:"))
        self.revival_points_spin = QSpinBox()
        self.revival_points_spin.setRange(1, 10)
        self.revival_points_spin.setValue(1)
        self.revival_points_spin.setEnabled(False)
        points_layout.addWidget(self.revival_points_spin)
        points_layout.addWidget(QLabel("points"))
        points_layout.addStretch()

        points_widget = QWidget()
        points_widget.setLayout(points_layout)
        options_layout.addRow("", points_widget)

        # Starting points option (indented - only available when revive with points is checked)
        starting_layout = QHBoxLayout()
        starting_layout.addWidget(QLabel("    "))  # Indentation
        self.revival_starting_check = QCheckBox("Use Starting Points")
        self.revival_starting_check.setToolTip("Use starting points instead of spinner value")
        self.revival_starting_check.setEnabled(False)
        self.revival_starting_check.stateChanged.connect(self.on_revival_starting_changed)
        starting_layout.addWidget(self.revival_starting_check)
        starting_layout.addStretch()

        starting_widget = QWidget()
        starting_widget.setLayout(starting_layout)
        options_layout.addRow("", starting_widget)
        
        # Revive within turn limit (renamed from Turn Delay) - Updated default per glossary V1.0.1
        self.revival_within_turn_spin = QSpinBox()
        self.revival_within_turn_spin.setRange(0, 20)
        self.revival_within_turn_spin.setValue(1)  # Default should be 1 per glossary V1.0.1
        self.revival_within_turn_spin.setSuffix(" turns (0=no limit)")
        self.revival_within_turn_spin.setToolTip("Time limit for revival availability")
        options_layout.addRow("Revive Within:", self.revival_within_turn_spin)
        
        layout.addLayout(options_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize revival targets list
        if not hasattr(self, 'revival_targets'):
            self.revival_targets = []
        # NOTE: List display updates are handled automatically by DialogDataManager
    
    def add_enhanced_range_config(self):
        """Enhanced range configuration with new options"""
        # Note: Starting tile inclusion is now handled in the range editor dialog
        pass
    
    def add_pass_through_config(self):
        """Pass through configuration with enhanced options"""
        group = QGroupBox("🚶 Pass Through Configuration")
        layout = QVBoxLayout()
        
        # Duplicate adjacency required layout structure
        info_label = QLabel("Move through another unit's square. Use adjacency settings to define which pieces allow pass-through.")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(info_label)
        
        # Enhanced inline piece selector for pass through pieces
        from dialogs.inline_selectors import InlinePieceSelector
        self.pass_through_selector = InlinePieceSelector(self, "Pass Through Pieces", allow_costs=True)
        self.pass_through_selector.pieces_changed.connect(self.on_pass_through_pieces_changed)
        layout.addWidget(self.pass_through_selector)

        # Pass through range configuration
        range_group = QGroupBox("Pass Through Range")
        range_layout = QVBoxLayout()

        range_info = QLabel("Define custom range pattern for pass through targeting:")
        range_info.setStyleSheet("color: #666; font-style: italic;")
        range_layout.addWidget(range_info)

        range_btn_layout = QHBoxLayout()
        self.pass_through_range_btn = QPushButton("Edit Pass Through Range")
        self.pass_through_range_btn.clicked.connect(self.edit_pass_through_range)
        self.pass_through_range_btn.setToolTip("Define custom range pattern for pass through targeting")
        range_btn_layout.addWidget(self.pass_through_range_btn)
        range_btn_layout.addStretch()

        range_layout.addLayout(range_btn_layout)
        range_group.setLayout(range_layout)
        layout.addWidget(range_group)

        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize pass through pieces list
        if not hasattr(self, 'pass_through_pieces'):
            self.pass_through_pieces = []
        # NOTE: List display updates are handled automatically by DialogDataManager
    
    def add_los_required_config(self):
        """Line of sight required configuration with enhanced options"""
        group = QGroupBox("👁️ Line of Sight Required Configuration")
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Requires line of sight to target."))
        
        options_layout = QFormLayout()
        
        # Ignore Friendly checkbox - Updated per glossary V1.0.1
        self.los_ignore_enemy_check = QCheckBox("Ignore Friendly (no block)")
        self.los_ignore_enemy_check.setToolTip("Friendly pieces don't block line of sight")
        options_layout.addRow("Friendly Blocking:", self.los_ignore_enemy_check)
        
        # Ignore All checkbox
        self.los_ignore_all_check = QCheckBox("Ignore All (blocks nothing)")
        self.los_ignore_all_check.setToolTip("No pieces block line of sight")
        options_layout.addRow("All Blocking:", self.los_ignore_all_check)
        
        # Make mutually exclusive
        self.los_ignore_enemy_check.stateChanged.connect(self.on_los_ignore_changed)
        self.los_ignore_all_check.stateChanged.connect(self.on_los_ignore_changed)
        
        layout.addLayout(options_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    def add_delay_config(self):
        """Delay configuration with enhanced options"""
        group = QGroupBox("⏰ Delay Configuration")
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Delays the ability's effect."))
        
        # Delay type selection
        delay_layout = QFormLayout()
        
        # Turn Delay
        turn_delay_layout = QHBoxLayout()
        self.delay_turn_check = QCheckBox("Turn Delay:")
        turn_delay_layout.addWidget(self.delay_turn_check)
        self.delay_turn_spin = QSpinBox()
        self.delay_turn_spin.setRange(1, 20)
        self.delay_turn_spin.setValue(1)
        self.delay_turn_spin.setSuffix(" turns")
        self.delay_turn_spin.setEnabled(False)
        turn_delay_layout.addWidget(self.delay_turn_spin)
        turn_delay_layout.addStretch()
        delay_layout.addRow("", turn_delay_layout)
        
        # Action Delay
        action_delay_layout = QHBoxLayout()
        self.delay_action_check = QCheckBox("Action Delay:")
        action_delay_layout.addWidget(self.delay_action_check)
        self.delay_action_spin = QSpinBox()
        self.delay_action_spin.setRange(1, 20)
        self.delay_action_spin.setValue(1)
        self.delay_action_spin.setSuffix(" actions")
        self.delay_action_spin.setEnabled(False)
        action_delay_layout.addWidget(self.delay_action_spin)
        action_delay_layout.addStretch()
        delay_layout.addRow("", action_delay_layout)
        
        # Connect checkboxes
        self.delay_turn_check.stateChanged.connect(
            lambda state: self.delay_turn_spin.setEnabled(state == Qt.CheckState.Checked.value)
        )
        self.delay_action_check.stateChanged.connect(
            lambda state: self.delay_action_spin.setEnabled(state == Qt.CheckState.Checked.value)
        )
        
        layout.addLayout(delay_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    def add_pulse_effect_config(self):
        """Pulse effect configuration"""
        group = QGroupBox("🔄 Pulse Effect Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Triggers the current ability on a recurring interval."))
        
        form_layout = QFormLayout()
        
        # Only interval spinner (presence of tag = enabled) - Updated default per glossary V1.0.1
        self.pulse_interval_spin = QSpinBox()
        self.pulse_interval_spin.setRange(1, 20)
        self.pulse_interval_spin.setValue(1)  # Default should be 1 per glossary V1.0.1
        self.pulse_interval_spin.setSuffix(" turns")
        self.pulse_interval_spin.setToolTip("Repeat interval in turns")
        form_layout.addRow("Repeat Every:", self.pulse_interval_spin)
        
        layout.addLayout(form_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    def add_fog_of_war_config(self):
        """Fog of war configuration with enhanced options"""
        group = QGroupBox("🌫️ Fog of War Configuration")
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Reveals tiles on the board around the source piece."))
        
        form_layout = QFormLayout()
        
        # Vision type
        self.fog_vision_combo = QComboBox()
        self.fog_vision_combo.addItems([f"{code} - {desc}" for code, desc in VISION_TYPES])
        self.fog_vision_combo.setToolTip("Type of vision system")
        self.fog_vision_combo.currentTextChanged.connect(self.on_fog_vision_changed)
        form_layout.addRow("Vision Type:", self.fog_vision_combo)
        
        # Range Configuration (both sight and lantern)
        range_layout = QHBoxLayout()
        self.fog_radius_spin = QSpinBox()
        self.fog_radius_spin.setRange(1, 8)
        self.fog_radius_spin.setValue(2)
        self.fog_radius_spin.setToolTip("Radius of tiles to reveal")
        range_layout.addWidget(self.fog_radius_spin)
        
        self.fog_custom_range_btn = QPushButton("Custom Range")
        self.fog_custom_range_btn.clicked.connect(self.edit_fog_custom_range)
        self.fog_custom_range_btn.setToolTip("Define custom vision pattern")
        range_layout.addWidget(self.fog_custom_range_btn)
        range_layout.addStretch()
        form_layout.addRow("Range:", range_layout)
        
        # Duration (for lantern type)
        self.fog_duration_spin = QSpinBox()
        self.fog_duration_spin.setRange(0, 20)
        self.fog_duration_spin.setValue(0)
        self.fog_duration_spin.setSuffix(" turns")
        self.fog_duration_spin.setSpecialValueText("Permanent")
        self.fog_duration_spin.setToolTip("Duration of reveal effect (0 for permanent)")
        self.fog_duration_spin.setVisible(False)
        form_layout.addRow("Duration:", self.fog_duration_spin)
        
        # Cost (for lantern type)
        self.fog_cost_spin = QSpinBox()
        self.fog_cost_spin.setRange(0, 10)
        self.fog_cost_spin.setValue(1)
        self.fog_cost_spin.setToolTip("Additional cost for lantern vision")
        self.fog_cost_spin.setVisible(False)
        form_layout.addRow("Extra Cost:", self.fog_cost_spin)
        
        layout.addLayout(form_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
    
    def add_carry_piece_config(self):
        """Carry piece configuration with enhanced options"""
        group = QGroupBox("🎒 Carry Piece Configuration")
        layout = QVBoxLayout()
        
        # Basic carry piece description
        layout.addWidget(QLabel("Allow this piece to carry other pieces."))
        
        # Enhanced inline piece selector for carry targets
        from dialogs.inline_selectors import InlinePieceSelector
        self.carry_target_selector = InlinePieceSelector(self, "Carry Targets", allow_costs=True)
        self.carry_target_selector.pieces_changed.connect(self.on_carry_targets_changed)
        layout.addWidget(self.carry_target_selector)
        
        # Enhanced carry options
        options_layout = QFormLayout()

        # Carry range (updated per glossary: use range editor for custom patterns)
        carry_range_layout = QHBoxLayout()
        self.carry_range_spin = QSpinBox()
        self.carry_range_spin.setRange(0, 8)
        self.carry_range_spin.setValue(0)
        self.carry_range_spin.setToolTip("Distance for picking up pieces (0=self, 1-8=range)")
        carry_range_layout.addWidget(self.carry_range_spin)

        self.carry_range_custom_btn = QPushButton("Custom Range")
        self.carry_range_custom_btn.clicked.connect(self.edit_carry_range_pattern)
        self.carry_range_custom_btn.setToolTip("Define custom carry range pattern")
        carry_range_layout.addWidget(self.carry_range_custom_btn)
        carry_range_layout.addStretch()
        options_layout.addRow("Carry Range:", carry_range_layout)

        # Drop options (new per glossary)
        self.carry_drop_on_death_check = QCheckBox("Drop carried pieces when carrier dies")
        self.carry_drop_on_death_check.stateChanged.connect(self.on_carry_drop_on_death_changed)
        options_layout.addRow("Drop on Death:", self.carry_drop_on_death_check)

        self.carry_drop_mode_combo = QComboBox()
        self.carry_drop_mode_combo.addItems(["random", "selectable"])  # Removed "self"
        self.carry_drop_mode_combo.setToolTip("How dropped pieces are placed")
        self.carry_drop_mode_combo.setVisible(False)  # Hidden by default
        options_layout.addRow("Drop Mode:", self.carry_drop_mode_combo)

        # Drop range with custom option (updated per glossary)
        drop_range_layout = QHBoxLayout()
        self.carry_drop_range_spin = QSpinBox()
        self.carry_drop_range_spin.setRange(0, 10)  # Updated to 0-10 range
        self.carry_drop_range_spin.setValue(1)
        self.carry_drop_range_spin.setToolTip("Distance for dropping pieces (0-10, or use custom)")
        drop_range_layout.addWidget(self.carry_drop_range_spin)

        self.carry_drop_range_custom_btn = QPushButton("Custom Drop Range")
        self.carry_drop_range_custom_btn.clicked.connect(self.edit_carry_drop_range_pattern)
        self.carry_drop_range_custom_btn.setToolTip("Define custom drop range pattern")
        drop_range_layout.addWidget(self.carry_drop_range_custom_btn)
        drop_range_layout.addStretch()

        # Initially hide drop range options
        self.carry_drop_range_widget = QWidget()
        self.carry_drop_range_widget.setLayout(drop_range_layout)
        self.carry_drop_range_widget.setVisible(False)
        options_layout.addRow("Drop Range:", self.carry_drop_range_widget)

        self.carry_drop_can_capture_check = QCheckBox("Dropped pieces can capture")
        self.carry_drop_can_capture_check.setVisible(False)  # Hidden by default
        options_layout.addRow("Drop Capture:", self.carry_drop_can_capture_check)

        # Share Abilities
        self.carry_share_abilities_check = QCheckBox("Share Abilities")
        self.carry_share_abilities_check.setToolTip("Carried piece can use abilities while being carried")
        options_layout.addRow("Abilities:", self.carry_share_abilities_check)

        # Starting Piece (simple checkbox)
        self.carry_starting_piece_check = QCheckBox("Piece starts carrying another piece")
        self.carry_starting_piece_check.setToolTip("Enable if this piece should start the game already carrying another piece")
        options_layout.addRow("Starting Piece:", self.carry_starting_piece_check)
        
        layout.addLayout(options_layout)
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize carry targets list
        if not hasattr(self, 'carry_targets'):
            self.carry_targets = []
        # NOTE: List display updates are handled automatically by DialogDataManager
    
    def add_enhanced_adjacency_required_config(self):
        """Enhanced adjacency required configuration with tile selection"""
        group = QGroupBox("🔗 Enhanced Adjacency Required Configuration")
        layout = QVBoxLayout()
        
        layout.addWidget(QLabel("Only activates if adjacent to defined units."))
        
        # Existing adjacency piece list...
        
        # Add tile selection overlay
        tile_layout = QVBoxLayout()
        tile_layout.addWidget(QLabel("Specific Adjacent Tiles (relative to piece):"))
        
        # Create 3x3 grid for adjacency positions
        self.adjacency_tile_widget = QWidget()
        self.adjacency_tile_widget.setFixedSize(120, 120)
        tile_grid_layout = QGridLayout(self.adjacency_tile_widget)
        tile_grid_layout.setSpacing(2)
        
        self.adjacency_tile_buttons = []
        for r in range(3):
            row = []
            for c in range(3):
                btn = QPushButton()
                btn.setFixedSize(35, 35)
                btn.setCheckable(True)
                if r == 1 and c == 1:  # Center (piece position)
                    btn.setText("♔")
                    btn.setEnabled(False)
                    btn.setStyleSheet("background: #3399ff; color: white; font-weight: bold;")
                else:
                    btn.clicked.connect(lambda checked, row=r, col=c: self.toggle_adjacency_tile(row, col, checked))
                tile_grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.adjacency_tile_buttons.append(row)
        
        tile_layout.addWidget(self.adjacency_tile_widget)
        layout.addLayout(tile_layout)
        
        group.setLayout(layout)
        self.config_content_layout.addWidget(group)
        
        # Initialize adjacency tile pattern
        if not hasattr(self, 'adjacency_tile_pattern'):
            self.adjacency_tile_pattern = [[False for _ in range(3)] for _ in range(3)]
        self.update_adjacency_tile_display()
    
    # ========== CALLBACK METHODS FOR NEW CONFIGURATIONS ==========
    
    def delete_current_ability(self):
        """Delete the currently loaded ability"""
        if not self.current_filename:
            QMessageBox.warning(self, "Warning", "No ability is currently loaded.")
            return
        
        ability_name = self.current_ability.get('name', 'Unknown')
        reply = QMessageBox.question(
            self, "Confirm Delete", 
            f"Are you sure you want to delete the ability '{ability_name}'?\n\nThis action cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                import os
                if os.path.exists(self.current_filename):
                    os.remove(self.current_filename)
                    QMessageBox.information(self, "Success", f"Ability '{ability_name}' has been deleted.")
                    self.reset_form()
                    self.refresh_ability_list()
                    self.status_label.setText("Ability deleted")
                else:
                    QMessageBox.critical(self, "Error", f"File not found: {self.current_filename}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to delete ability:\n{str(e)}")
    


    # Buff effect toggle callbacks
    def on_buff_add_ability_changed(self, state):
        """Handle buff add ability checkbox change"""
        is_checked = state == Qt.CheckState.Checked.value
        # Show/hide the inline ability selector
        if hasattr(self, 'buff_ability_selector'):
            self.buff_ability_selector.setVisible(is_checked)
        # Legacy support for old widgets
        if hasattr(self, 'buff_ability_list'):
            self.buff_ability_list.setVisible(is_checked)
        if hasattr(self, 'buff_add_ability_btn'):
            self.buff_add_ability_btn.setVisible(is_checked)
        if hasattr(self, 'buff_remove_ability_btn'):
            self.buff_remove_ability_btn.setVisible(is_checked)
    
    def on_buff_enhance_movement_changed(self, state):
        """Handle buff enhance movement checkbox change"""
        is_checked = state == Qt.CheckState.Checked.value
        if hasattr(self, 'buff_movement_btn'):
            self.buff_movement_btn.setVisible(is_checked)
    
    def on_buff_enhance_attack_changed(self, state):
        """Handle buff enhance attack checkbox change"""
        is_checked = state == Qt.CheckState.Checked.value
        if hasattr(self, 'buff_attack_range_spin'):
            self.buff_attack_range_spin.setVisible(is_checked)
    
    # Debuff effect toggle callbacks
    def on_debuff_prevent_ability_changed(self, state):
        """Handle debuff prevent ability checkbox change"""
        is_checked = state == Qt.CheckState.Checked.value
        # Show/hide the inline ability selector
        if hasattr(self, 'debuff_prevent_ability_selector'):
            self.debuff_prevent_ability_selector.setVisible(is_checked)
        # Legacy support for old widgets
        if hasattr(self, 'debuff_prevent_ability_list'):
            self.debuff_prevent_ability_list.setVisible(is_checked)
        if hasattr(self, 'debuff_add_prevent_btn'):
            self.debuff_add_prevent_btn.setVisible(is_checked)
        if hasattr(self, 'debuff_remove_prevent_btn'):
            self.debuff_remove_prevent_btn.setVisible(is_checked)
    
    def on_debuff_reduce_movement_changed(self, state):
        """Handle debuff reduce movement checkbox change"""
        is_checked = state == Qt.CheckState.Checked.value
        if hasattr(self, 'debuff_movement_btn'):
            self.debuff_movement_btn.setVisible(is_checked)
    
    def on_debuff_reduce_attack_changed(self, state):
        """Handle debuff reduce attack checkbox change"""
        is_checked = state == Qt.CheckState.Checked.value
        if hasattr(self, 'debuff_attack_range_spin'):
            self.debuff_attack_range_spin.setVisible(is_checked)
    
    # Enhanced LoS callbacks
    def on_los_ignore_changed(self, state):
        """Handle LoS ignore checkbox changes (make mutually exclusive)"""
        sender = self.sender()
        if state == Qt.CheckState.Checked.value:
            if sender == self.los_ignore_enemy_check and hasattr(self, 'los_ignore_all_check'):
                self.los_ignore_all_check.setChecked(False)
            elif sender == self.los_ignore_all_check and hasattr(self, 'los_ignore_enemy_check'):
                self.los_ignore_enemy_check.setChecked(False)
    
    # Fog of war callbacks
    def on_fog_vision_changed(self, text):
        """Handle fog vision type change"""
        is_lantern = "lantern" in text.lower()
        if hasattr(self, 'fog_duration_spin'):
            self.fog_duration_spin.setVisible(is_lantern)
        if hasattr(self, 'fog_cost_spin'):
            self.fog_cost_spin.setVisible(is_lantern)
    
    # Adjacency tile callbacks (enhanced version)
    def toggle_adjacency_tile_enhanced(self, row, col, checked):
        """Toggle adjacency tile selection for enhanced adjacency"""
        if hasattr(self, 'adjacency_tile_pattern'):
            self.adjacency_tile_pattern[row][col] = checked
            self.update_adjacency_tile_display()
    
    # ========== NEW CALLBACK METHODS FOR UPDATED CONFIGURATIONS ==========
    
    # Buff/Debuff movement pattern callbacks (consolidated)
    def on_buff_movement_pattern_changed(self, state):
        """Handle buff movement pattern toggle"""
        enabled = state == Qt.CheckState.Checked.value
        if hasattr(self, 'buff_movement_pattern_btn'):
            self.buff_movement_pattern_btn.setVisible(enabled)

    def on_debuff_movement_pattern_changed(self, state):
        """Handle debuff movement pattern toggle"""
        enabled = state == Qt.CheckState.Checked.value
        if hasattr(self, 'debuff_movement_pattern_btn'):
            self.debuff_movement_pattern_btn.setVisible(enabled)
    
    def edit_buff_movement_pattern(self):
        """Edit buff movement and attack pattern using single pattern editor"""
        from dialogs.pattern_editor_dialog import edit_single_pattern

        # Initialize pattern if not exists
        if not hasattr(self, 'buff_movement_pattern'):
            self.buff_movement_pattern = [[0 for _ in range(8)] for _ in range(8)]

        # Get current checkbox states from ability data
        checkbox_states = self.current_ability.get('buffPatternCheckboxStates', {})

        # Edit pattern using single pattern editor with 5-color system
        new_pattern, new_checkbox_states = edit_single_pattern(
            self.buff_movement_pattern,
            "Buff Movement & Attack Pattern",
            self,
            checkbox_states
        )

        if new_pattern is not None:
            self.buff_movement_pattern = new_pattern
            # Save checkbox states to ability data
            self.current_ability['buffPatternCheckboxStates'] = new_checkbox_states
            QMessageBox.information(self, "Pattern Saved", "Buff movement and attack pattern has been updated.")
    
    def edit_debuff_movement_pattern(self):
        """Edit debuff movement and attack pattern using single pattern editor"""
        from dialogs.pattern_editor_dialog import edit_single_pattern

        # Initialize pattern if not exists
        if not hasattr(self, 'debuff_movement_pattern'):
            self.debuff_movement_pattern = [[0 for _ in range(8)] for _ in range(8)]

        # Get current checkbox states from ability data
        checkbox_states = self.current_ability.get('debuffPatternCheckboxStates', {})

        # Edit pattern using single pattern editor with 5-color system
        new_pattern, new_checkbox_states = edit_single_pattern(
            self.debuff_movement_pattern,
            "Debuff Movement & Attack Pattern",
            self,
            checkbox_states
        )

        if new_pattern is not None:
            self.debuff_movement_pattern = new_pattern
            # Save checkbox states to ability data
            self.current_ability['debuffPatternCheckboxStates'] = new_checkbox_states
            QMessageBox.information(self, "Pattern Saved", "Debuff movement and attack pattern has been updated.")
    
    # Trap ability selector callback
    def select_trap_ability(self):
        """Select ability for trap embedded effect using inline selector"""
        from dialogs.inline_selectors import InlineAbilitySelector
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QPushButton, QHBoxLayout

        # Create a simple dialog with inline ability selector
        dialog = QDialog(self)
        dialog.setWindowTitle("Select Trap Ability")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout(dialog)

        # Add inline ability selector
        selector = InlineAbilitySelector(dialog, "Trap Ability")
        layout.addWidget(selector)

        # Add buttons
        button_layout = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Cancel")

        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        # Show dialog and get result
        if dialog.exec() == QDialog.DialogCode.Accepted and selector.abilities:
            # Use the first selected ability
            ability_data = selector.abilities[0]
            if hasattr(self, 'trap_ability_label'):
                self.trap_ability_label.setText(f"{ability_data['ability']} (Cost: {ability_data['cost']})")
                self.trap_ability_label.setStyleSheet("color: palette(text); font-style: normal;")
            # Store the selected ability data
            self.trap_ability_data = ability_data
    
    # Fog of war custom range callback
    def edit_fog_custom_range(self):
        """Edit custom fog of war range pattern"""
        from dialogs.range_editor_dialog import edit_range_pattern
        
        # Initialize pattern if not exists
        if not hasattr(self, 'fog_custom_range_pattern'):
            self.fog_custom_range_pattern = [[False for _ in range(8)] for _ in range(8)]
        
        # Edit range pattern
        new_pattern = edit_range_pattern(
            self.fog_custom_range_pattern,
            "Fog of War Custom Range Pattern",
            self
        )
        
        if new_pattern is not None:
            self.fog_custom_range_pattern = new_pattern
            QMessageBox.information(self, "Range Saved", "Fog of war custom range pattern has been updated.")
    
    # ========== NEW CALLBACK METHODS FOR ADDITIONAL IMPROVEMENTS ==========
    
    # Revival callbacks
    def on_revival_sacrifice_changed(self, state):
        """Handle revival sacrifice mode toggle"""
        enabled = state == Qt.CheckState.Checked.value
        if hasattr(self, 'revival_max_cost_spin'):
            self.revival_max_cost_spin.setEnabled(enabled)
    
    def on_revival_points_changed(self, state):
        """Handle revival points toggle"""
        enabled = state == Qt.CheckState.Checked.value
        if hasattr(self, 'revival_points_spin'):
            self.revival_points_spin.setEnabled(enabled)
        if hasattr(self, 'revival_starting_check'):
            self.revival_starting_check.setEnabled(enabled)
    
    def on_revival_starting_changed(self, state):
        """Handle revival starting points toggle"""
        enabled = state == Qt.CheckState.Checked.value
        if hasattr(self, 'revival_points_spin'):
            self.revival_points_spin.setEnabled(not enabled)  # Disable spinner when using starting points
    
    # Carry piece callbacks
    def on_carry_drop_on_death_changed(self, state):
        """Handle carry drop on death checkbox toggle"""
        enabled = state == Qt.CheckState.Checked.value
        if hasattr(self, 'carry_drop_mode_combo'):
            self.carry_drop_mode_combo.setVisible(enabled)
        if hasattr(self, 'carry_drop_range_widget'):
            self.carry_drop_range_widget.setVisible(enabled)
        if hasattr(self, 'carry_drop_can_capture_check'):
            self.carry_drop_can_capture_check.setVisible(enabled)

    def edit_carry_range_pattern(self):
        """Edit carry range pattern using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'carry_range_pattern', None)
        initial_position = getattr(self, 'carry_range_piece_position', [3, 3])
        checkbox_states = self.current_ability.get('carryRangeCheckboxStates', {})

        pattern, piece_position, new_checkbox_states = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Carry Range Pattern", self, checkbox_states
        )

        if pattern is not None:
            self.carry_range_pattern = pattern
            self.carry_range_piece_position = piece_position
            self.current_ability['carryRangeCheckboxStates'] = new_checkbox_states
            QMessageBox.information(self, "Pattern Saved", "Carry range pattern has been updated.")

    def edit_carry_drop_range_pattern(self):
        """Edit carry drop range pattern using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'carry_drop_range_pattern', None)
        initial_position = getattr(self, 'carry_drop_range_piece_position', [3, 3])
        checkbox_states = self.current_ability.get('carryDropRangeCheckboxStates', {})

        pattern, piece_position, new_checkbox_states = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Carry Drop Range Pattern", self, checkbox_states
        )

        if pattern is not None:
            self.carry_drop_range_pattern = pattern
            self.carry_drop_range_piece_position = piece_position
            self.current_ability['carryDropRangeCheckboxStates'] = new_checkbox_states
            QMessageBox.information(self, "Pattern Saved", "Carry drop range pattern has been updated.")

    # Note: select_carry_starting_piece method removed - now using simple checkbox
    
    # Immobilize callbacks
    def on_immobilize_duration_changed(self, state):
        """Handle immobilize duration checkbox toggle"""
        enabled = state == Qt.CheckState.Checked.value
        if hasattr(self, 'immobilize_duration_spin'):
            self.immobilize_duration_spin.setEnabled(enabled)
    
    # NOTE: Old dialog methods for revival and carry targets have been removed
    # These configurations now use inline selectors with automatic data management

    def update_adjacency_tile_display(self):
        """Update adjacency tile display"""
        if not hasattr(self, 'adjacency_tile_buttons') or not hasattr(self, 'adjacency_tile_pattern'):
            return
        
        for r in range(3):
            for c in range(3):
                if r == 1 and c == 1:  # Skip center (piece position)
                    continue
                btn = self.adjacency_tile_buttons[r][c]
                if self.adjacency_tile_pattern[r][c]:
                    btn.setStyleSheet("background: #ffcc66; border: 2px solid #cc8800; font-weight: bold;")
                    btn.setText("✓")
                else:
                    btn.setStyleSheet("background: #f0f0f0; border: 1px solid #ccc;")
                    btn.setText("")
    


    # NOTE: Movement pattern editing methods are implemented in the ability tag sections above
    
    def add_debuff_prevent_ability(self):
        """Add ability to debuff prevent list - handled by inline selector"""
        # This functionality is now handled by the inline ability selector
        pass

    def edit_debuff_prevent_ability(self):
        """Edit selected debuff prevent ability - handled by inline selector"""
        # This functionality is now handled by the inline ability selector
        pass

    def remove_debuff_prevent_ability(self):
        """Remove selected debuff prevent ability - handled by inline selector"""
        # This functionality is now handled by the inline ability selector
        pass

    def edit_trap_teleport_range(self):
        """Edit trap teleport range using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'trap_teleport_range', None)
        initial_position = getattr(self, 'trap_teleport_piece_position', [3, 3])

        pattern, piece_position, _ = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Trap Teleport Range", self
        )

        if pattern is not None:
            self.trap_teleport_range = pattern
            self.trap_teleport_piece_position = piece_position
            QMessageBox.information(self, "Pattern Saved", "Trap teleport range pattern has been updated.")

    def edit_pass_through_range(self):
        """Edit pass through range pattern using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'pass_through_range_pattern', None)
        initial_position = getattr(self, 'pass_through_piece_position', [3, 3])
        checkbox_states = self.current_ability.get('passThroughRangeCheckboxStates', {})

        pattern, piece_position, new_checkbox_states = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Pass Through Range Pattern", self, checkbox_states
        )

        if pattern is not None:
            self.pass_through_range_pattern = pattern
            self.pass_through_piece_position = piece_position
            self.current_ability['passThroughRangeCheckboxStates'] = new_checkbox_states
            QMessageBox.information(self, "Pattern Saved", "Pass through range pattern has been updated.")

    # NOTE: Old dialog methods for reaction targets have been removed
    # These configurations now use inline selectors with automatic data management

    # ========== MISSING CALLBACK METHODS ==========

    def toggle_adjacency_tile_alt(self, row, col, checked):
        """Toggle adjacency tile selection (alternative implementation)"""
        if hasattr(self, 'adjacency_tile_pattern'):
            self.adjacency_tile_pattern[row][col] = checked
            self.update_adjacency_tile_display_alt()

    def update_adjacency_tile_display_alt(self):
        """Update adjacency tile display (alternative implementation)"""
        if (not hasattr(self, 'adjacency_tile_buttons') or
            not hasattr(self, 'adjacency_tile_pattern')):
            return

        for r in range(3):
            for c in range(3):
                if r == 1 and c == 1:  # Skip center (piece position)
                    continue
                try:
                    btn = self.adjacency_tile_buttons[r][c]
                    if self.adjacency_tile_pattern[r][c]:
                        btn.setStyleSheet("background: #ffcc66; border: 2px solid #cc8800; font-weight: bold;")
                        btn.setText("✓")
                    else:
                        btn.setStyleSheet("background: #f0f0f0; border: 1px solid #ccc;")
                        btn.setText("")
                except (IndexError, TypeError, AttributeError):
                    # Skip if buttons or pattern not properly initialized
                    continue

# ========== LEGACY PIECE LIST MANAGER REMOVED ==========
# All piece list operations now handled by DialogDataManager


# ========== MAIN EXECUTION ==========

if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    window = AbilityEditorWindow()
    window.show()
    sys.exit(app.exec())
    