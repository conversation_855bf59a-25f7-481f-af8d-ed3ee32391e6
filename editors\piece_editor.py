"""
Piece Editor for Adventure Chess
Focused on piece management - ability editing is handled in ability_editor.py

!! NO VALIDATION POLICY !!
This editor allows MAXIMUM CUSTOMIZATION with no validation constraints.
Users can select any combination of abilities, movement patterns, and settings.
The ONLY validation is name validation when saving files.
Save operations store ONLY what is displayed in the UI for full flexibility.
"""
# Standard library imports
import os

# PyQt6 imports
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton,
    QComboBox, QFileDialog, QGroupBox, QSpinBox,
    QListWidget, QListWidgetItem, QCheckBox, QScrollArea, QGridLayout,
    QDialog, QAbstractItemView, QMessageBox, QMainWindow, QSizePolicy, QMenu,
    QFormLayout
)
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import Qt

# Local imports
from config import (
    PIECES_DIR, ICONS_DIR, RECHARGE_TYPES,
    PIECE_EDITOR_DEFAULT, PIECE_EDITOR_MIN, PIECE_ROLES
)
from utils.utils import save_json_file
from ui.ui_utils import ResponsiveScrollArea, ResponsiveLayout, setup_responsive_window
from ui.ui_shared_components import FileOperationsWidget, ValidationStatusWidget
# Data management - using only Pydantic system
from utils.simple_bridge import simple_bridge

# Streamlined data management
from editors.base_editor import BaseEditor

# Dialogs - lazy import to avoid circular dependencies
from dialogs.piece_ability_manager import PieceAbilityManagerDialog


class PromotionSelectorDialog(QDialog):
    """Dialog for selecting promotion pieces with checkboxes"""
    
    def __init__(self, parent=None, title="Select Promotion Pieces", selected_pieces=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setMinimumSize(400, 500)
        
        self.selected_pieces = selected_pieces.copy() if selected_pieces else []
        self.piece_checkboxes = {}
        
        self.setup_ui()
        self.refresh_piece_list()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel(f"""
        <b>{self.windowTitle()}</b><br>
        Select pieces that this piece can promote to. Check the boxes for pieces you want to include.
        """)
        instructions.setWordWrap(True)
        instructions.setStyleSheet("padding: 10px; background-color: palette(base); color: palette(text); border: 1px solid palette(mid); border-radius: 4px;")
        layout.addWidget(instructions)
        
        # Search bar
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("🔍 Search:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Type to filter pieces...")
        self.search_edit.textChanged.connect(self.filter_pieces)
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)
        
        # Scroll area for checkboxes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(300)
        
        self.checkboxes_widget = QWidget()
        self.checkboxes_layout = QVBoxLayout()
        self.checkboxes_widget.setLayout(self.checkboxes_layout)
        scroll_area.setWidget(self.checkboxes_widget)
        
        layout.addWidget(scroll_area)
        
        # Selection info
        self.selection_info = QLabel("0 pieces selected")
        self.selection_info.setStyleSheet("font-weight: bold; color: #666;")
        layout.addWidget(self.selection_info)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("Select All")
        select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(select_all_btn)
        
        clear_all_btn = QPushButton("Clear All")
        clear_all_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(clear_all_btn)
        
        button_layout.addStretch()
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        ok_btn = QPushButton("OK")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        ok_btn.setStyleSheet("QPushButton { background-color: #4caf50; color: white; font-weight: bold; }")
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def refresh_piece_list(self):
        """Refresh the list of available pieces"""
        # Clear existing checkboxes
        for checkbox in self.piece_checkboxes.values():
            checkbox.setParent(None)
        self.piece_checkboxes.clear()
        
        if not os.path.exists(PIECES_DIR):
            no_pieces_label = QLabel("No pieces directory found")
            no_pieces_label.setStyleSheet("color: #666; font-style: italic;")
            self.checkboxes_layout.addWidget(no_pieces_label)
            return
        
        # Get all JSON files in pieces directory
        piece_files = [f for f in os.listdir(PIECES_DIR) if f.lower().endswith('.json')]
        
        if not piece_files:
            no_pieces_label = QLabel("No piece files found in data/pieces/")
            no_pieces_label.setStyleSheet("color: #666; font-style: italic;")
            self.checkboxes_layout.addWidget(no_pieces_label)
            return
        
        for piece_file in sorted(piece_files):
            # Use simple bridge for data loading
            try:
                base_filename = piece_file.replace('.json', '')
                piece_data, error = simple_bridge.load_piece_for_ui(base_filename)
                if piece_data and not error:
                    piece_name = piece_data.get('name', piece_file[:-5])  # Remove .json extension
                    display_text = f"{piece_name}"
                    tooltip_text = f"File: {piece_file}\nDescription: {piece_data.get('description', 'No description')}"
                else:
                    piece_name = piece_file[:-5]
                    display_text = f"❌ {piece_name} (Error loading)"
                    tooltip_text = f"File: {piece_file}\nError: {error or 'Unknown error'}"
            except Exception as e:
                piece_name = piece_file[:-5]
                display_text = f"❌ {piece_name} (Error loading)"
                tooltip_text = f"File: {piece_file}\nError: {str(e)}"
            
            # Create checkbox
            checkbox = QCheckBox(display_text)
            checkbox.setToolTip(tooltip_text)
            checkbox.setChecked(piece_name in self.selected_pieces)
            checkbox.stateChanged.connect(self.update_selection_info)
            
            self.piece_checkboxes[piece_name] = checkbox
            self.checkboxes_layout.addWidget(checkbox)
        
        self.update_selection_info()
    
    def filter_pieces(self):
        """Filter pieces based on search text"""
        search_text = self.search_edit.text().lower()
        
        for piece_name, checkbox in self.piece_checkboxes.items():
            checkbox_text = checkbox.text().lower()
            checkbox.setVisible(search_text in checkbox_text or search_text in piece_name.lower())
    
    def select_all(self):
        """Select all visible pieces"""
        for checkbox in self.piece_checkboxes.values():
            if checkbox.isVisible():
                checkbox.setChecked(True)
    
    def clear_all(self):
        """Clear all selections"""
        for checkbox in self.piece_checkboxes.values():
            checkbox.setChecked(False)
    
    def update_selection_info(self):
        """Update the selection information"""
        selected_count = sum(1 for checkbox in self.piece_checkboxes.values() if checkbox.isChecked())
        total_count = len(self.piece_checkboxes)
        self.selection_info.setText(f"{selected_count} of {total_count} pieces selected")
    
    def get_selected_pieces(self):
        """Get the list of selected piece names"""
        selected = []
        for piece_name, checkbox in self.piece_checkboxes.items():
            if checkbox.isChecked():
                selected.append(piece_name)
        return selected



class PieceEditorWindow(BaseEditor):
    """
    Main Piece Editor Window - Focused on piece properties and ability references only
    All ability editing is handled in the separate Ability Editor
    """

    def __init__(self):
        # Initialize base editor with piece data type
        super().__init__("piece")

        self.current_custom_pattern = [[0 for _ in range(8)] for _ in range(8)]
        self.custom_pattern_piece_pos = [3, 3]  # Default piece position in center
        self.selected_movement_type = "orthogonal"  # Default movement type
        self.current_movement_data = {"type": "orthogonal", "pattern": None, "piecePosition": [3, 3]}  # Default movement data
        self.setWindowTitle("Piece Editor - Adventure Chess")
        
        # Setup responsive window
        setup_responsive_window(self, PIECE_EDITOR_DEFAULT, PIECE_EDITOR_MIN)
        
        # Create main widget with scroll area
        scroll_area = ResponsiveScrollArea()
        main_layout = scroll_area.content_layout

        # File Operations at the top - using shared component
        file_ops_group = QGroupBox("File Operations")
        file_ops_layout = QVBoxLayout()

        # Create shared file operations widget
        self.file_ops_widget = FileOperationsWidget("full")

        # Connect signals to piece-specific methods
        self.file_ops_widget.connect_signals(
            new_func=self.new_piece,
            load_func=self.load_piece_from_dropdown,
            save_func=self.save_piece,
            save_as_func=self.save_as_piece,
            delete_func=self.delete_piece
        )

        # Customize button text for piece-specific operations
        self.file_ops_widget.new_btn.setText("📄 New Piece")
        self.file_ops_widget.load_btn.setText("📂 Load Piece")
        self.file_ops_widget.save_btn.setText("💾 Save Piece")
        self.file_ops_widget.delete_btn.setText("🗑️ Delete Piece")

        file_ops_layout.addWidget(self.file_ops_widget)
        file_ops_group.setLayout(file_ops_layout)
        main_layout.addWidget(file_ops_group)

        # Simple Load Piece Dropdown - Always visible
        load_piece_layout = QHBoxLayout()
        load_piece_layout.addWidget(QLabel("Load Piece:"))

        # Simple searchable combobox
        self.load_piece_combo = QComboBox()
        self.load_piece_combo.setEditable(True)  # Makes it searchable
        self.load_piece_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)  # Prevent adding new items
        self.load_piece_combo.setPlaceholderText("🔍 Type to search or select piece...")
        self.load_piece_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.load_piece_combo.setStyleSheet("""
            QComboBox {
                background-color: #2d3748;
                color: #e2e8f0;
                border: 1px solid #4a5568;
                border-radius: 4px;
                padding: 6px;
                font-size: 12px;
                min-width: 200px;
            }
            QComboBox:editable {
                background-color: #2d3748;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 1px solid #4a5568;
                width: 8px;
                height: 8px;
                background: #4a5568;
            }
            QComboBox QAbstractItemView {
                background-color: #2d3748;
                color: #e2e8f0;
                border: 1px solid #4a5568;
                selection-background-color: #4a5568;
            }
        """)

        # Load button
        load_btn = QPushButton("📂 Load")
        load_btn.clicked.connect(self.load_piece_from_dropdown)
        load_btn.setToolTip("Load the selected piece")

        load_piece_layout.addWidget(self.load_piece_combo)
        load_piece_layout.addWidget(load_btn)
        load_piece_layout.addStretch()

        main_layout.addLayout(load_piece_layout)

        # Populate the dropdown
        self.refresh_load_piece_dropdown()

        # Piece Name
        name_layout = ResponsiveLayout.create_hbox(margin=0, spacing=5)
        name_layout.addWidget(QLabel("Piece Name:"))
        self.name_edit = QLineEdit()
        self.name_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.name_edit.textChanged.connect(self.mark_unsaved_changes)
        name_layout.addWidget(self.name_edit)
        main_layout.addLayout(name_layout)

        # Piece Description - Updated per glossary V1.0.1
        desc_layout = ResponsiveLayout.create_hbox(margin=0, spacing=5)
        desc_layout.addWidget(QLabel("Piece Description:"))
        self.desc_edit = QTextEdit()
        self.desc_edit.setFixedHeight(50)
        self.desc_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.desc_edit.textChanged.connect(self.mark_unsaved_changes)
        desc_layout.addWidget(self.desc_edit)

        # Summary button per glossary V1.0.1
        self.summary_btn = QPushButton("📝 Summary")
        self.summary_btn.clicked.connect(self.show_summary_message)
        self.summary_btn.setToolTip("This will eventually create a summary based on your inputs")
        desc_layout.addWidget(self.summary_btn)

        main_layout.addLayout(desc_layout)

        # Piece Role and Special Properties
        role_layout = ResponsiveLayout.create_hbox(margin=0, spacing=10)
        role_layout.addWidget(QLabel("Piece Role:"))
        self.role_combo = QComboBox()
        for role_name, _ in PIECE_ROLES:
            self.role_combo.addItem(role_name)
        self.role_combo.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        self.role_combo.setToolTip("Commander: Your key piece affected by check/checkmate\nSupporter: A supporter of your Commander")
        self.role_combo.currentTextChanged.connect(self.on_role_changed)
        role_layout.addWidget(self.role_combo)
        
        # Castling checkbox (initially hidden)
        self.can_castle_check = QCheckBox("Can Castle")
        self.can_castle_check.setToolTip("Allows this piece to participate in castling moves with other castling pieces")
        self.can_castle_check.setVisible(False)  # Hidden by default
        self.can_castle_check.stateChanged.connect(self.mark_unsaved_changes)
        role_layout.addWidget(self.can_castle_check)
        
        # Track Starting Position checkbox
        self.track_starting_position_check = QCheckBox("Track Starting Position")
        self.track_starting_position_check.setToolTip("Track whether this piece has moved from its starting position (useful for special moves like castling, en passant, or first-move abilities)")
        self.track_starting_position_check.stateChanged.connect(self.mark_unsaved_changes)
        role_layout.addWidget(self.track_starting_position_check)
        
        # Color Directional Movement checkbox
        self.color_directional_check = QCheckBox("Color Directional")
        self.color_directional_check.setToolTip("White pieces move up the board, black pieces move down")
        self.color_directional_check.stateChanged.connect(self.mark_unsaved_changes)
        role_layout.addWidget(self.color_directional_check)

        # Can Capture checkbox
        self.can_capture_check = QCheckBox("Can Capture")
        self.can_capture_check.setToolTip("Whether this piece can capture enemy pieces")
        self.can_capture_check.setChecked(True)  # Default to True
        self.can_capture_check.stateChanged.connect(self.mark_unsaved_changes)
        role_layout.addWidget(self.can_capture_check)

        role_layout.addStretch()
        main_layout.addLayout(role_layout)

        # Icon selection (dropdown + file picker + preview)
        icon_layout = QHBoxLayout()
        icon_layout.addWidget(QLabel("Black Icon:"))
        self.black_icon_combo = QComboBox()
        self.black_icon_combo.currentTextChanged.connect(self.update_icon_previews)
        self.black_icon_picker = QPushButton("Pick File")
        self.black_icon_picker.clicked.connect(lambda: self.select_icon('black'))
        self.black_icon_preview = QLabel()
        self.black_icon_preview.setFixedSize(40, 40)
        icon_layout.addWidget(self.black_icon_combo)
        icon_layout.addWidget(self.black_icon_picker)
        icon_layout.addWidget(self.black_icon_preview)
        icon_layout.addSpacing(20)
        icon_layout.addWidget(QLabel("White Icon:"))
        self.white_icon_combo = QComboBox()
        self.white_icon_combo.currentTextChanged.connect(self.update_icon_previews)
        self.white_icon_picker = QPushButton("Pick File")
        self.white_icon_picker.clicked.connect(lambda: self.select_icon('white'))
        self.white_icon_preview = QLabel()
        self.white_icon_preview.setFixedSize(40, 40)
        icon_layout.addWidget(self.white_icon_combo)
        icon_layout.addWidget(self.white_icon_picker)
        icon_layout.addWidget(self.white_icon_preview)
        self.refresh_icon_combos()
        main_layout.addLayout(icon_layout)
        self.update_icon_previews()

        # Movement Pattern - Quick Option Buttons
        move_layout = QVBoxLayout()

        # Title and buttons row
        pattern_header_layout = QHBoxLayout()
        pattern_header_layout.addWidget(QLabel("Movement Pattern:"))
        pattern_header_layout.addStretch()

        # Quick pattern buttons with chess piece symbols
        self.movement_pattern_buttons = []
        movement_buttons = [
            ("♜", "orthogonal", "Orthogonal lines (like Rook)"),
            ("♝", "diagonal", "Diagonal lines (like Bishop)"),
            ("♛", "any", "All directions (like Queen)"),
            ("♞", "lShape", "L-shaped moves (like Knight)"),
            ("♚", "king", "King movement (1 square any direction)"),
            ("🌐", "global", "Global movement (anywhere on board)"),
            ("🎨", "custom", "Custom pattern (opens pattern editor)")
        ]

        for symbol, movement_type, tooltip in movement_buttons:
            btn = QPushButton(symbol)
            btn.setFixedSize(35, 30)
            btn.setToolTip(f"{tooltip}")
            btn.setCheckable(True)  # Make buttons selectable
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #3a3a3a, stop:1 #2a2a2a);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a4a4a, stop:1 #3a3a3a);
                    border-color: #66aaff;
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4488cc, stop:1 #3366aa);
                    border-color: #66aaff;
                    border-width: 3px;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #1a1a1a, stop:1 #2a2a2a);
                    border-color: #4488cc;
                }
            """)
            btn.clicked.connect(lambda checked, mt=movement_type, b=btn: self.select_movement_preset(mt, b))
            pattern_header_layout.addWidget(btn)
            self.movement_pattern_buttons.append((btn, movement_type))

        move_layout.addLayout(pattern_header_layout)

        # Note: Movement patterns are stored in the pattern editor
        # Quick buttons provide easy access to standard patterns
        
        # Movement options in organized layout
        movement_options_layout = QHBoxLayout()

        # Movement Pattern Preview - 8x8 Grid
        preview_group = QVBoxLayout()
        preview_group.addWidget(QLabel("Movement Preview:"))

        # Create 8x8 preview grid widget
        self.movement_pattern_preview = QWidget()
        self.movement_pattern_preview.setFixedSize(120, 120)
        self.movement_pattern_preview.setStyleSheet("""
            QWidget {
                border: 2px solid #4a5568;
                background: #2d3748;
                border-radius: 6px;
            }
        """)

        # Create grid layout for the preview
        self.preview_grid_layout = QGridLayout(self.movement_pattern_preview)
        self.preview_grid_layout.setSpacing(1)
        self.preview_grid_layout.setContentsMargins(2, 2, 2, 2)

        # Create 8x8 grid of small buttons for preview
        self.preview_grid_buttons = []
        for row in range(8):
            button_row = []
            for col in range(8):
                btn = QPushButton()
                btn.setFixedSize(13, 13)
                btn.setEnabled(False)  # Preview only, not interactive
                self.preview_grid_layout.addWidget(btn, row, col)
                button_row.append(btn)
            self.preview_grid_buttons.append(button_row)

        preview_group.addWidget(self.movement_pattern_preview)
        movement_options_layout.addLayout(preview_group)

        movement_options_layout.addStretch()
        move_layout.addLayout(movement_options_layout)
        
        main_layout.addLayout(move_layout)
        self.update_movement_controls()

        # Points and Recharge System
        points_group = QGroupBox("Points and Recharge System")
        points_layout = ResponsiveLayout.create_form_layout()
        
        # Enable recharge system checkbox
        self.enable_recharge_check = QCheckBox("Enable Recharge System")
        self.enable_recharge_check.setToolTip("Enable the points and recharge system for this piece")
        self.enable_recharge_check.stateChanged.connect(self.on_recharge_enabled_changed)
        points_layout.addRow("", self.enable_recharge_check)
        
        self.max_points_spin = QSpinBox()
        self.max_points_spin.setRange(0, 99)
        self.max_points_spin.valueChanged.connect(self.mark_unsaved_changes)
        points_layout.addRow("Max Points:", self.max_points_spin)
        
        self.starting_points_spin = QSpinBox()
        self.starting_points_spin.setRange(0, 99)
        self.starting_points_spin.valueChanged.connect(self.mark_unsaved_changes)
        points_layout.addRow("Starting Points:", self.starting_points_spin)
        
        self.recharge_combo = QComboBox()
        for recharge_type, _ in RECHARGE_TYPES:
            self.recharge_combo.addItem(recharge_type)
        self.recharge_combo.setToolTip("turnRecharge: Gain points each turn | adjacencyRecharge: Gain points when near specific pieces | committedRecharge: Turns this piece cant move, take actions, or attack, after commitment you are fully recharged")
        self.recharge_combo.currentTextChanged.connect(self.on_recharge_changed)
        points_layout.addRow("Recharge Type:", self.recharge_combo)
        
        self.turn_points_spin = QSpinBox()
        self.turn_points_spin.setRange(1, 10)
        self.turn_points_spin.setValue(1)
        self.turn_points_spin.valueChanged.connect(self.mark_unsaved_changes)
        points_layout.addRow("Turn Points:", self.turn_points_spin)

        # Adjacency Recharge fields (initially hidden) - Using unified dialog
        self.adjacency_recharge_widget = QWidget()
        adjacency_recharge_layout = QVBoxLayout(self.adjacency_recharge_widget)
        adjacency_recharge_layout.setContentsMargins(0, 0, 0, 0)

        # Button to open unified adjacency dialog
        self.edit_adjacency_recharge_btn = QPushButton("🔗 Configure Adjacency Recharge")
        self.edit_adjacency_recharge_btn.clicked.connect(self.open_adjacency_recharge_dialog)
        self.edit_adjacency_recharge_btn.setToolTip("Open adjacency recharge configuration dialog")
        adjacency_recharge_layout.addWidget(self.edit_adjacency_recharge_btn)

        # Preview of current adjacency recharge configuration
        self.adjacency_recharge_preview_label = QLabel("No adjacency recharge configured")
        self.adjacency_recharge_preview_label.setStyleSheet("color: #666; font-style: italic; padding: 5px; border: 1px solid #ddd; background: #f9f9f9;")
        self.adjacency_recharge_preview_label.setWordWrap(True)
        adjacency_recharge_layout.addWidget(self.adjacency_recharge_preview_label)

        self.adjacency_recharge_widget.setVisible(False)
        points_layout.addRow("Adjacency:", self.adjacency_recharge_widget)

        # Initialize adjacency recharge data
        if not hasattr(self, 'adjacency_recharge_config'):
            self.adjacency_recharge_config = {
                'pieces': [],
                'distance': 1,
                'pattern': []
            }

        # Committed Recharge fields (initially hidden)
        self.committed_recharge_spin = QSpinBox()
        self.committed_recharge_spin.setRange(1, 10)
        self.committed_recharge_spin.setValue(1)
        self.committed_recharge_spin.setToolTip("Turns this piece can't move, attack, or take actions. Fully recharged at end")
        self.committed_recharge_spin.valueChanged.connect(self.mark_unsaved_changes)
        self.committed_recharge_spin.setVisible(False)
        points_layout.addRow("Committed Turns:", self.committed_recharge_spin)

        points_group.setLayout(points_layout)
        main_layout.addWidget(points_group)
        self.update_recharge_options()

        # Abilities - INLINE SELECTOR
        abilities_group = QGroupBox("Abilities")
        abilities_layout = QVBoxLayout()

        # Instructions
        abilities_info = QLabel("Select abilities for this piece using the inline selector below.")
        abilities_info.setWordWrap(True)
        abilities_info.setStyleSheet("color: #666; font-style: italic; padding: 5px;")
        abilities_layout.addWidget(abilities_info)

        # Use the InlineAbilitySelector
        from dialogs.inline_selectors import InlineAbilitySelector
        self.ability_selector = InlineAbilitySelector(self, "Piece Abilities")
        self.ability_selector.abilities_changed.connect(self.on_abilities_changed)
        abilities_layout.addWidget(self.ability_selector)


        abilities_group.setLayout(abilities_layout)
        main_layout.addWidget(abilities_group)
        
        # Initialize abilities list
        self.abilities = []  # List of ability file references
        # Note: Ability selection handling simplified per glossary V1.0.1

        # Promotion Options
        promo_group = QGroupBox("Promotions")
        promo_layout = QVBoxLayout()
        
        # Primary Promotions
        primary_promo_layout = QHBoxLayout()
        primary_promo_layout.addWidget(QLabel("Primary Promotions:"))
        self.primary_promo_btn = QPushButton("Select Pieces...")
        self.primary_promo_btn.clicked.connect(self.open_primary_promotion_selector)
        primary_promo_layout.addWidget(self.primary_promo_btn)
        primary_promo_layout.addStretch()
        promo_layout.addLayout(primary_promo_layout)
        
        # Primary promotions display
        self.primary_promo_display = QLabel("None selected")
        self.primary_promo_display.setStyleSheet("padding: 5px; background-color: palette(base); color: palette(text); border: 1px solid palette(mid); border-radius: 3px;")
        self.primary_promo_display.setWordWrap(True)
        promo_layout.addWidget(self.primary_promo_display)
        
        # Secondary Promotions - Updated per glossary V1.0.1
        secondary_promo_layout = QHBoxLayout()
        secondary_promo_layout.addWidget(QLabel("Secondary Promotions:"))
        secondary_promo_info = QLabel("(A promoted piece gets a secondary promotion if it gets to its friendly back rank)")
        secondary_promo_info.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        secondary_promo_layout.addWidget(secondary_promo_info)
        self.secondary_promo_btn = QPushButton("Select Pieces...")
        self.secondary_promo_btn.clicked.connect(self.open_secondary_promotion_selector)
        secondary_promo_layout.addWidget(self.secondary_promo_btn)
        secondary_promo_layout.addStretch()
        promo_layout.addLayout(secondary_promo_layout)
        
        # Secondary promotions display
        self.secondary_promo_display = QLabel("None selected")
        self.secondary_promo_display.setStyleSheet("padding: 5px; background-color: palette(base); color: palette(text); border: 1px solid palette(mid); border-radius: 3px;")
        self.secondary_promo_display.setWordWrap(True)
        promo_layout.addWidget(self.secondary_promo_display)
        
        promo_group.setLayout(promo_layout)
        main_layout.addWidget(promo_group)
        
        # Initialize promotion lists
        self.primary_promotions = []
        self.secondary_promotions = []

        # Status display using shared component
        self.status_widget = ValidationStatusWidget()
        main_layout.addWidget(self.status_widget)

        # Console/Log
        self.log_console = QTextEdit()
        self.log_console.setMaximumHeight(100)
        self.log_console.setReadOnly(True)
        self.log_console.setPlaceholderText("Console output will appear here...")
        main_layout.addWidget(QLabel("Console:"))
        main_layout.addWidget(self.log_console)

        # Set main widget
        self.setCentralWidget(scroll_area)
        
        # Initialize with default values
        self.reset_form()
        self.status_widget.show_info("Piece Editor initialized. Ready to create or edit pieces.")
        self.log_console.append("Piece Editor initialized. Ready to create or edit pieces.")

    # ========== ZOOM FUNCTIONALITY ==========

    def zoom_in(self):
        """Zoom in the editor content"""
        # Find the main scroll area and zoom it
        scroll_areas = self.findChildren(ResponsiveScrollArea)
        for scroll_area in scroll_areas:
            scroll_area.zoom_in()

    def zoom_out(self):
        """Zoom out the editor content"""
        # Find the main scroll area and zoom it
        scroll_areas = self.findChildren(ResponsiveScrollArea)
        for scroll_area in scroll_areas:
            scroll_area.zoom_out()

    def reset_zoom(self):
        """Reset zoom to 100%"""
        # Find the main scroll area and reset zoom
        scroll_areas = self.findChildren(ResponsiveScrollArea)
        for scroll_area in scroll_areas:
            scroll_area.reset_zoom()
    
    def mark_unsaved_changes(self):
        """Mark that there are unsaved changes"""
        self.unsaved_changes = True
        if not self.windowTitle().endswith("*"):
            self.setWindowTitle(self.windowTitle() + "*")
    
    def clear_unsaved_changes(self):
        """Clear the unsaved changes flag"""
        self.unsaved_changes = False
        title = self.windowTitle()
        if title.endswith("*"):
            self.setWindowTitle(title[:-1])
    
    def on_role_changed(self):
        """Handle role change"""
        role = self.role_combo.currentText()
        # Show castling option for Commander and Supporter roles
        self.can_castle_check.setVisible(role in ["Commander", "Supporter"])
        self.mark_unsaved_changes()
    
    def select_movement_preset(self, movement_type, button):
        """Select a movement preset and store full movement data"""
        # Clear other button selections
        for btn, _ in self.movement_pattern_buttons:
            if btn != button:
                btn.setChecked(False)

        # Apply the movement type if button is checked
        if button.isChecked():
            self.apply_movement_preset(movement_type)
            self.selected_movement_type = movement_type
        else:
            # If unchecked, default to orthogonal
            self.apply_movement_preset("orthogonal")
            self.selected_movement_type = "orthogonal"
            # Check the orthogonal button
            for btn, mt in self.movement_pattern_buttons:
                if mt == "orthogonal":
                    btn.setChecked(True)
                    break

        self.update_movement_controls()
        self.mark_unsaved_changes()

    def on_movement_changed(self):
        """Handle movement type change (legacy compatibility)"""
        self.update_movement_controls()
        self.mark_unsaved_changes()
    
    def apply_movement_preset(self, movement_type):
        """Apply a movement preset by generating the standard pattern"""
        if movement_type == "custom":
            # Custom movement - open pattern editor with current pattern
            self.current_movement_data = {
                "type": "custom",
                "pattern": self.current_custom_pattern if hasattr(self, 'current_custom_pattern') else None,
                "piecePosition": self.custom_pattern_piece_pos if hasattr(self, 'custom_pattern_piece_pos') else [3, 3]
            }
            # Open pattern editor for custom movement
            self.open_custom_pattern_editor()
        else:
            # Generate standard pattern for the movement type
            piece_pos = getattr(self, 'custom_pattern_piece_pos', [3, 3])
            standard_pattern = self.generate_standard_pattern(movement_type, piece_pos)

            # Store the movement data with generated pattern
            self.current_movement_data = {
                "type": movement_type,
                "pattern": standard_pattern,
                "piecePosition": piece_pos
            }

            # Update the current custom pattern to match the standard pattern
            self.current_custom_pattern = standard_pattern

            # Update pattern preview
            self.update_movement_pattern_preview()

    def detect_movement_pattern_type(self, movement_data):
        """
        Detect if movement data matches a quick pattern or is custom
        Returns: (pattern_type, is_quick_pattern)
        """
        if not movement_data:
            return "orthogonal", True

        movement_type = movement_data.get('type', 'orthogonal')

        # Check if it's a standard quick pattern type
        quick_patterns = ['orthogonal', 'diagonal', 'any', 'lShape', 'king', 'global']
        if movement_type in quick_patterns:
            # For standard types, check if there's a custom pattern
            if 'pattern' in movement_data and movement_data['pattern']:
                # Has custom pattern - need to verify if it matches the standard pattern
                return self.verify_pattern_matches_type(movement_data)
            else:
                # No custom pattern, it's a standard quick pattern
                return movement_type, True
        elif movement_type == 'custom':
            # Explicitly marked as custom
            return "custom", False
        else:
            # Unknown type, treat as custom
            return "custom", False

    def verify_pattern_matches_type(self, movement_data):
        """
        Verify if a custom pattern actually matches a standard movement type
        Returns: (pattern_type, is_quick_pattern)
        """
        pattern = movement_data.get('pattern')
        piece_pos = movement_data.get('piecePosition', [3, 3])
        movement_type = movement_data.get('type', 'orthogonal')

        if not pattern or not isinstance(pattern, list):
            return movement_type, True

        # Generate expected pattern for the movement type
        expected_pattern = self.generate_standard_pattern(movement_type, piece_pos)

        # Compare patterns (allowing for different values as long as structure matches)
        if self.patterns_match_structure(pattern, expected_pattern):
            return movement_type, True
        else:
            # Pattern doesn't match standard type, it's custom
            return "custom", False

    def generate_standard_pattern(self, movement_type, piece_pos):
        """Generate the standard pattern for a movement type"""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        piece_r, piece_c = piece_pos

        if movement_type == "orthogonal":
            # Rook pattern: orthogonal lines
            for c in range(8):
                if c != piece_c:
                    pattern[piece_r][c] = 3
            for r in range(8):
                if r != piece_r:
                    pattern[r][piece_c] = 3

        elif movement_type == "diagonal":
            # Bishop pattern: diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            pattern[r][c] = 3

        elif movement_type == "any":
            # Queen pattern: orthogonal + diagonal
            for c in range(8):
                if c != piece_c:
                    pattern[piece_r][c] = 3
            for r in range(8):
                if r != piece_r:
                    pattern[r][piece_c] = 3
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            pattern[r][c] = 3

        elif movement_type == "lShape":
            # Knight pattern: L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2),
                           (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    pattern[r][c] = 3

        elif movement_type == "king":
            # King pattern: 1 square in any direction
            king_moves = [(-1, -1), (-1, 0), (-1, 1),
                         (0, -1),           (0, 1),
                         (1, -1),  (1, 0),  (1, 1)]
            for dr, dc in king_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    pattern[r][c] = 3

        elif movement_type == "global":
            # Global pattern: anywhere on the board
            for r in range(8):
                for c in range(8):
                    if r != piece_r or c != piece_c:  # Exclude piece position
                        pattern[r][c] = 3

        return pattern

    def patterns_match_structure(self, pattern1, pattern2):
        """Check if two patterns have the same structure (non-zero positions match)"""
        if len(pattern1) != 8 or len(pattern2) != 8:
            return False

        for r in range(8):
            if len(pattern1[r]) != 8 or len(pattern2[r]) != 8:
                return False
            for c in range(8):
                # Check if both are non-zero or both are zero
                p1_nonzero = pattern1[r][c] != 0
                p2_nonzero = pattern2[r][c] != 0
                if p1_nonzero != p2_nonzero:
                    return False

        return True

    def update_movement_controls(self):
        """Update movement controls based on selected type"""
        # Get current movement type from button selection or fallback to combo
        movement_type = getattr(self, 'selected_movement_type', None)
        if not movement_type and hasattr(self, 'move_combo'):
            movement_type = self.move_combo.currentText().lower()

        # Update movement pattern preview
        self.update_movement_pattern_preview()
    
    def on_recharge_changed(self):
        """Handle recharge type change"""
        self.update_recharge_options()
        self.mark_unsaved_changes()
    
    def on_recharge_enabled_changed(self):
        """Handle recharge system enable/disable"""
        enabled = self.enable_recharge_check.isChecked()
        
        # Enable/disable recharge-related controls
        self.max_points_spin.setEnabled(enabled)
        self.starting_points_spin.setEnabled(enabled)
        self.recharge_combo.setEnabled(enabled)
        self.turn_points_spin.setEnabled(enabled)
        
        self.mark_unsaved_changes()
    
    def update_recharge_options(self):
        """Update recharge options based on selected type"""
        # Update enabled state based on checkbox
        self.on_recharge_enabled_changed()

        # Show/hide specific recharge type fields
        recharge_type = self.recharge_combo.currentText()
        enabled = self.enable_recharge_check.isChecked()

        # Turn Points - only for turnRecharge
        self.turn_points_spin.setVisible(enabled and recharge_type == "turnRecharge")

        # Adjacency - only for adjacencyRecharge
        self.adjacency_recharge_widget.setVisible(enabled and recharge_type == "adjacencyRecharge")

        # Committed - only for committedRecharge
        self.committed_recharge_spin.setVisible(enabled and recharge_type == "committedRecharge")
    
    def refresh_icon_combos(self):
        """Refresh the icon combo boxes"""
        if not os.path.exists(ICONS_DIR):
            return
        
        icon_files = [f for f in os.listdir(ICONS_DIR) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
        
        self.black_icon_combo.clear()
        self.white_icon_combo.clear()
        
        self.black_icon_combo.addItem("(None)")
        self.white_icon_combo.addItem("(None)")
        
        for icon_file in sorted(icon_files):
            self.black_icon_combo.addItem(icon_file)
            self.white_icon_combo.addItem(icon_file)
    
    def select_icon(self, color):
        """Open file dialog to select an icon"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, f"Select {color.title()} Icon", ICONS_DIR,
            "Image Files (*.png *.jpg *.jpeg *.gif *.bmp)"
        )
        
        if file_path:
            # Copy file to icons directory
            filename = os.path.basename(file_path)
            dest_path = os.path.join(ICONS_DIR, filename)
            
            try:
                if file_path != dest_path:
                    shutil.copy2(file_path, dest_path)
                
                # Update combo box
                self.refresh_icon_combos()
                
                if color == 'black':
                    self.black_icon_combo.setCurrentText(filename)
                else:
                    self.white_icon_combo.setCurrentText(filename)
                
                self.log_console.append(f"Icon {filename} added for {color} pieces.")
                self.mark_unsaved_changes()
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to copy icon: {str(e)}")
    
    def update_icon_previews(self):
        """Update icon preview labels"""
        def set_preview(combo, preview_label):
            icon_name = combo.currentText()
            if icon_name and icon_name != "(None)":
                icon_path = os.path.join(ICONS_DIR, icon_name)
                if os.path.exists(icon_path):
                    pixmap = QPixmap(icon_path)
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(40, 40, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                        preview_label.setPixmap(scaled_pixmap)
                        # Set background to match dark theme
                        preview_label.setStyleSheet("border: 1px solid #4a5568; background: #2d3748; padding: 2px;")
                        return
            
            # Set placeholder with dark theme background
            preview_label.clear()  # Clear any existing pixmap
            preview_label.setText("No Icon")
            preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            preview_label.setStyleSheet("border: 1px solid #4a5568; background: #2d3748; color: #a0aec0; font-size: 10px; padding: 2px;")
        
        set_preview(self.black_icon_combo, self.black_icon_preview)
        set_preview(self.white_icon_combo, self.white_icon_preview)
    
    def open_custom_pattern_editor(self):
        """Open the custom pattern editor with current movement type highlighted"""
        from dialogs.pattern_editor_dialog import PatternEditorDialog

        # Map movement types to pattern editor preset types
        movement_to_preset_map = {
            'orthogonal': 'rook',
            'diagonal': 'bishop',
            'any': 'queen',
            'lShape': 'knight',
            'king': 'king',
            'global': 'global'
        }

        # Get current movement type for highlighting
        current_movement = getattr(self, 'selected_movement_type', 'orthogonal')
        preset_to_highlight = movement_to_preset_map.get(current_movement, None)

        # Prepare checkbox states with auto continue off board setting
        # King and Knight should NOT continue off board, all others should
        should_continue_off_board = current_movement not in ['king', 'lShape']

        checkbox_states = {
            'starting_square_checked': False,
            'continue_off_board_checked': should_continue_off_board
        }

        # Create dialog with highlighted preset and checkbox states
        dialog = PatternEditorDialog(
            movement_pattern=self.current_custom_pattern,
            title="Movement Pattern Editor",
            parent=self,
            checkbox_states=checkbox_states,
            highlighted_preset=preset_to_highlight
        )

        if dialog.exec() == dialog.DialogCode.Accepted:
            pattern, _ = dialog.get_pattern(), dialog.get_checkbox_states()
            if pattern is not None:
                self.current_custom_pattern = pattern
                # Update movement data to custom type since pattern was edited
                self.current_movement_data = {
                    "type": "custom",
                    "pattern": pattern,
                    "piecePosition": getattr(self, 'custom_pattern_piece_pos', [3, 3])
                }
                self.selected_movement_type = "custom"
                # Update button selection to custom
                if hasattr(self, 'movement_pattern_buttons'):
                    for btn, movement_type in self.movement_pattern_buttons:
                        btn.setChecked(movement_type == "custom")

                self.update_movement_pattern_preview()
                self.update_movement_controls()
                self.log_console.append("Custom pattern saved.")
                self.mark_unsaved_changes()
    
    def update_movement_pattern_preview(self):
        """Update the movement pattern preview with actual pattern colors"""
        if not hasattr(self, 'preview_grid_buttons') or not self.preview_grid_buttons:
            return

        # Get current movement type and pattern
        movement_type = getattr(self, 'selected_movement_type', 'orthogonal')

        # Generate or get the current pattern
        if hasattr(self, 'current_custom_pattern') and self.current_custom_pattern:
            pattern = self.current_custom_pattern
        else:
            # Generate standard pattern for preview
            piece_pos = [3, 3]  # Center position for preview
            pattern = self.generate_standard_pattern(movement_type, piece_pos)

        # Set continue off board checkbox based on movement type
        self.set_continue_off_board_for_movement_type(movement_type)

        # Update the 8x8 preview grid with pattern colors
        for row in range(8):
            for col in range(8):
                btn = self.preview_grid_buttons[row][col]

                if [row, col] == [3, 3]:  # Piece position (center)
                    # Piece position - blue with piece symbol
                    btn.setStyleSheet("""
                        QPushButton {
                            background: #3399ff;
                            border: 1px solid #0066cc;
                            font-size: 8px;
                            font-weight: bold;
                            color: white;
                        }
                    """)
                    btn.setText("♔")
                else:
                    # Get pattern state
                    state = pattern[row][col] if pattern and len(pattern) > row and len(pattern[row]) > col else 0
                    btn.setText("")

                    # Apply pattern editor color scheme
                    if state == 0:
                        # Empty - dark background
                        btn.setStyleSheet("""
                            QPushButton {
                                background: #2d3748;
                                border: 1px solid #4a5568;
                            }
                        """)
                    elif state == 1:
                        # Move only - blue
                        btn.setStyleSheet("""
                            QPushButton {
                                background: #4444ff;
                                border: 1px solid #0000aa;
                            }
                        """)
                    elif state == 2:
                        # Attack only - red
                        btn.setStyleSheet("""
                            QPushButton {
                                background: #ff4444;
                                border: 1px solid #aa0000;
                            }
                        """)
                    elif state == 3:
                        # Move and attack - purple
                        btn.setStyleSheet("""
                            QPushButton {
                                background: #aa44aa;
                                border: 1px solid #660066;
                            }
                        """)
                    elif state == 4:
                        # Action - yellow
                        btn.setStyleSheet("""
                            QPushButton {
                                background: #ffff44;
                                border: 1px solid #cccc00;
                            }
                        """)
                    else:  # state == 5
                        # Any - green
                        btn.setStyleSheet("""
                            QPushButton {
                                background: #66ff66;
                                border: 1px solid #44cc44;
                            }
                        """)

    def set_continue_off_board_for_movement_type(self, movement_type):
        """Set the continue off board checkbox based on movement type"""
        # King and Knight (lShape) should NOT continue off board
        # All other movement types should continue off board
        should_continue = movement_type not in ['king', 'lShape']

        # Store this setting for when the pattern editor is opened
        self.auto_continue_off_board = should_continue

    def update_custom_pattern_preview(self):
        """Legacy method - redirects to new preview system"""
        self.update_movement_pattern_preview()

    def open_adjacency_recharge_dialog(self):
        """Open the unified adjacency recharge configuration dialog"""
        from dialogs.unified_adjacency_dialog import edit_adjacency_config

        result = edit_adjacency_config(
            parent=self,
            initial_config=self.adjacency_recharge_config,
            dialog_type="adjacency_recharge"
        )

        if result is not None:
            self.adjacency_recharge_config = result
            self.update_adjacency_recharge_preview_text()
            self.mark_unsaved_changes()

    def update_adjacency_recharge_preview_text(self):
        """Update the adjacency recharge preview text"""
        if not self.adjacency_recharge_config['pieces'] and not self.adjacency_recharge_config['pattern']:
            self.adjacency_recharge_preview_label.setText("No adjacency recharge configured")
        else:
            pieces_text = f"{len(self.adjacency_recharge_config['pieces'])} piece types" if self.adjacency_recharge_config['pieces'] else "Any pieces"
            pattern_text = f"{len(self.adjacency_recharge_config['pattern'])} specific squares" if self.adjacency_recharge_config['pattern'] else "all squares"
            distance_text = f"within distance {self.adjacency_recharge_config['distance']}"

            self.adjacency_recharge_preview_label.setText(
                f"Recharge from: {pieces_text} in {pattern_text} {distance_text}"
            )

    def show_summary_message(self):
        """Show summary message per glossary V1.0.1"""
        QMessageBox.information(self, "Summary Feature", "This will eventually create a summary based on your inputs")
        self.log_console.append("Summary feature - coming soon.")
    
    # ===== SIMPLE LOAD PIECE DROPDOWN FUNCTIONALITY =====

    def refresh_load_piece_dropdown(self):
        """Refresh the simple load piece dropdown"""
        self.load_piece_combo.clear()
        self.load_piece_combo.addItem("-- Select a piece to load --", "")

        if not os.path.exists(PIECES_DIR):
            self.load_piece_combo.addItem("No pieces directory found", "")
            return

        piece_files = [f for f in os.listdir(PIECES_DIR) if f.lower().endswith('.json')]
        if not piece_files:
            self.load_piece_combo.addItem("No pieces found", "")
            return

        for piece_file in sorted(piece_files):
            # Use Pydantic bridge for consistent data loading with validation
            try:
                base_filename = piece_file.replace('.json', '')
                piece_data, error = simple_bridge.load_piece_for_ui(base_filename)
                if piece_data and not error:
                    piece_name = piece_data.get('name', piece_file[:-5])
                    display_text = f"{piece_name}"
                    tooltip_text = f"File: {piece_file}\nDescription: {piece_data.get('description', 'No description')}"
                else:
                    display_text = f"❌ {piece_file[:-5]} (Error)"
                    tooltip_text = f"File: {piece_file}\nError: {error or 'Unknown error'}"
            except Exception as e:
                display_text = f"❌ {piece_file[:-5]} (Error)"
                tooltip_text = f"File: {piece_file}\nError: {str(e)}"

            self.load_piece_combo.addItem(display_text, piece_file)
            # Set tooltip for the last added item
            self.load_piece_combo.setItemData(self.load_piece_combo.count() - 1, tooltip_text, Qt.ItemDataRole.ToolTipRole)

    def load_piece_from_dropdown(self):
        """Load the selected piece from the simple dropdown"""
        current_index = self.load_piece_combo.currentIndex()
        if current_index <= 0:  # First item is placeholder
            QMessageBox.information(self, "No Selection", "Please select a piece to load.")
            return

        piece_filename = self.load_piece_combo.currentData()
        if not piece_filename:
            return

        # Check for unsaved changes
        if self.unsaved_changes:
            reply = QMessageBox.question(
                self, "Unsaved Changes",
                "You have unsaved changes. Do you want to save before loading?",
                QMessageBox.StandardButton.Save | QMessageBox.StandardButton.Discard | QMessageBox.StandardButton.Cancel
            )

            if reply == QMessageBox.StandardButton.Save:
                if not self.save_piece():
                    return
            elif reply == QMessageBox.StandardButton.Cancel:
                return

        # Load the piece using simple bridge
        try:
            base_filename = piece_filename.replace('.json', '')
            piece_data, error = simple_bridge.load_piece_for_ui(base_filename)
            if error:
                QMessageBox.critical(self, "Error", f"Failed to load piece: {error}")
                return

            self.load_piece_data(piece_data)

            # Set the current filename so saves go to the original file
            self.current_filename = base_filename

            self.status_widget.show_success(f"Loaded piece: {piece_data.get('name', 'Unknown')}")
            self.log_console.append(f"Loaded piece from {piece_filename} - will save to same file")
            self.clear_unsaved_changes()

            # Reset dropdown to placeholder
            self.load_piece_combo.setCurrentIndex(0)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load piece: {str(e)}")
    
    # ===== INLINE ABILITY MANAGEMENT =====

    def on_abilities_changed(self):
        """Handle changes to the abilities list from the inline selector"""
        # Get the current abilities from the selector
        self.abilities = self.ability_selector.get_abilities()
        # Mark as changed
        self.mark_unsaved_changes()

    def refresh_abilities_list(self):
        """Refresh the inline ability selector to match current abilities"""
        if hasattr(self, 'ability_selector'):
            self.ability_selector.set_abilities(self.abilities)
    
    # Note: Ability manager dialog removed - now using inline selection
    

    
    # Note: Old ability context menu and removal methods removed - now using inline checkboxes
    
    # ===== PIECE FILE OPERATIONS =====
    
    def new_piece(self):
        """Create a new piece using standardized BaseEditor method"""
        self.new_data()  # Use BaseEditor's standardized new_data method
    
    def load_piece(self):
        """Load a piece from file using standardized BaseEditor method"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Piece", PIECES_DIR, "JSON Files (*.json)"
        )

        if file_path:
            filename = os.path.basename(file_path).replace('.json', '')
            self.load_data(filename)  # Use BaseEditor's standardized load_data method
    
    # ===== PROMOTION SELECTOR FUNCTIONALITY =====
    
    def open_primary_promotion_selector(self):
        """Open the primary promotion selector dialog"""
        dialog = PromotionSelectorDialog(
            self, 
            "Select Primary Promotion Pieces", 
            self.primary_promotions
        )
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.primary_promotions = dialog.get_selected_pieces()
            self.update_promotion_displays()
            self.mark_unsaved_changes()
    
    def open_secondary_promotion_selector(self):
        """Open the secondary promotion selector dialog"""
        dialog = PromotionSelectorDialog(
            self, 
            "Select Secondary Promotion Pieces", 
            self.secondary_promotions
        )
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.secondary_promotions = dialog.get_selected_pieces()
            self.update_promotion_displays()
            self.mark_unsaved_changes()
    
    def update_promotion_displays(self):
        """Update the promotion display labels"""
        # Primary promotions
        if self.primary_promotions:
            primary_text = ", ".join(self.primary_promotions)
            self.primary_promo_display.setText(primary_text)
            self.primary_promo_display.setStyleSheet("padding: 5px; background-color: palette(highlight); color: palette(highlighted-text); border: 1px solid palette(highlight); border-radius: 3px;")
        else:
            self.primary_promo_display.setText("None selected")
            self.primary_promo_display.setStyleSheet("padding: 5px; background-color: palette(base); color: palette(text); border: 1px solid palette(mid); border-radius: 3px;")

        # Secondary promotions
        if self.secondary_promotions:
            secondary_text = ", ".join(self.secondary_promotions)
            self.secondary_promo_display.setText(secondary_text)
            self.secondary_promo_display.setStyleSheet("padding: 5px; background-color: palette(highlight); color: palette(highlighted-text); border: 1px solid palette(highlight); border-radius: 3px;")
        else:
            self.secondary_promo_display.setText("None selected")
            self.secondary_promo_display.setStyleSheet("padding: 5px; background-color: palette(base); color: palette(text); border: 1px solid palette(mid); border-radius: 3px;")
    
    def open_piece_dialog(self):
        """Alias for load_piece to maintain compatibility with main.py"""
        self.load_piece()
    
    def save_piece(self):
        """Save the current piece using standardized BaseEditor method"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Warning", "Please enter a piece name before saving.")
            return False

        return self.save_data()  # Use BaseEditor's standardized save_data method
    
    def save_as_piece(self) -> bool:
        """Save the piece with a new name using standardized BaseEditor method"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Warning", "Please enter a piece name before saving.")
            return False

        suggested_name = self.name_edit.text().replace(" ", "_").lower() + ".json"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Piece As", os.path.join(PIECES_DIR, suggested_name), "JSON Files (*.json)"
        )

        if file_path:
            filename = os.path.basename(file_path).replace('.json', '')
            return self.save_data(filename)  # Use BaseEditor's standardized save_data method

        return False
    
    def delete_piece(self):
        """Delete the current piece file"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Warning", "No piece loaded to delete.")
            return
        
        filename = self.name_edit.text().replace(" ", "_").lower() + ".json"
        file_path = os.path.join(PIECES_DIR, filename)
        
        if not os.path.exists(file_path):
            QMessageBox.warning(self, "Warning", f"Piece file {filename} not found.")
            return
        
        reply = QMessageBox.question(
            self, "Delete Piece", 
            f"Are you sure you want to delete the piece '{self.name_edit.text()}'?\n\nThis action cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                os.remove(file_path)
                self.log_console.append(f"Deleted piece {filename}")
                self.reset_form()
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to delete piece: {str(e)}")
    

    
    # ========== STREAMLINED DATA MANAGEMENT ==========
    # Using BaseEditor's standardized data collection

    # get_piece_data() is now replaced by collect_widget_data() inherited from BaseEditor
    # This eliminates the duplicate data collection pattern

    # Old collect_all_piece_widget_data method removed - now using BaseEditor's standardized approach

    def load_piece_data(self, piece_data):
        """Load piece data into the form using standardized BaseEditor approach"""
        try:
            print(f"📂 Loading piece data with {len(piece_data)} fields...")

            # Reset pattern data to defaults before loading
            self.reset_pattern_data()

            # Use BaseEditor's standardized widget setting
            self.set_widget_values_from_data(piece_data)

            # Load movement pattern data specifically
            self.load_movement_pattern_data(piece_data)

            # Update UI components after loading
            self.update_movement_controls()
            self.update_recharge_options()
            self.update_icon_previews()
            self.update_movement_pattern_preview()
            self.refresh_abilities_list()  # Refresh abilities display
            self.on_role_changed()

            print("✓ Piece data loaded and validated successfully")

        except Exception as e:
            print(f"❌ Error loading piece data: {e}")

    def reset_pattern_data(self):
        """Reset pattern data to defaults before loading new piece"""
        self.current_custom_pattern = [[0 for _ in range(8)] for _ in range(8)]
        self.custom_pattern_piece_pos = [3, 3]
        self.selected_movement_type = "orthogonal"
        self.current_movement_data = {"type": "orthogonal", "pattern": None, "piecePosition": [3, 3]}

    def load_movement_pattern_data(self, piece_data):
        """Load movement pattern data from piece data"""
        if 'movement' in piece_data:
            movement = piece_data['movement']

            # Set movement type
            movement_type = movement.get('type', 'orthogonal')
            self.selected_movement_type = movement_type

            # Load pattern if it exists
            if 'pattern' in movement:
                self.current_custom_pattern = movement['pattern']
                # Only set to custom if the movement type is actually custom
                # Standard types (orthogonal, diagonal, etc.) can also have patterns

            # Load piece position if it exists
            if 'piecePosition' in movement:
                self.custom_pattern_piece_pos = movement['piecePosition']

            # Update movement data
            self.current_movement_data = {
                "type": self.selected_movement_type,
                "pattern": self.current_custom_pattern,
                "piecePosition": self.custom_pattern_piece_pos
            }

            # If it's a standard movement type, generate the pattern
            if movement_type != "custom" and 'pattern' not in movement:
                standard_pattern = self.generate_standard_pattern(movement_type, self.custom_pattern_piece_pos)
                self.current_custom_pattern = standard_pattern
                self.current_movement_data["pattern"] = standard_pattern

            print(f"✓ Loaded movement pattern: {movement_type}")

            # Update movement button selection
            if hasattr(self, 'movement_pattern_buttons'):
                for btn, btn_movement_type in self.movement_pattern_buttons:
                    btn.setChecked(btn_movement_type == self.selected_movement_type)

    # set_all_piece_widget_values() is now replaced by set_widget_values_from_data() inherited from BaseEditor
    # This eliminates the duplicate widget setting pattern

    # ========== BASEEDITOR ABSTRACT METHOD IMPLEMENTATIONS ==========

    def reset_form(self):
        """Reset the piece editor form to default state (BaseEditor abstract method)"""
        print("🧹 Resetting piece editor form...")

        # Create default piece structure
        default_piece = {
            "version": "1.0.0",
            "name": "",
            "description": "",
            "role": "Commander",
            "canCastle": False,
            "movement": {"type": "orthogonal", "pattern": None, "piecePosition": [3, 3]},
            "canCapture": True,
            "colorDirectional": False,
            "abilities": [],
            "maxPoints": 3,
            "startingPoints": 2,
            "rechargeType": "turnRecharge"
        }

        # Set default data
        self.current_data = default_piece

        # Reset form fields directly (with safety checks)
        if hasattr(self, 'name_edit'):
            self.name_edit.clear()
        if hasattr(self, 'desc_edit'):
            self.desc_edit.clear()
        if hasattr(self, 'max_points_spin'):
            self.max_points_spin.setValue(3)  # Default max points
        if hasattr(self, 'starting_points_spin'):
            self.starting_points_spin.setValue(2)  # Default starting points

        # Reset role
        if hasattr(self, 'role_combo'):
            self.role_combo.setCurrentText("Commander")  # Default role

        # Reset movement - select orthogonal button and generate pattern
        self.selected_movement_type = "orthogonal"
        # Generate default orthogonal pattern
        default_pattern = self.generate_standard_pattern("orthogonal", [3, 3])
        self.current_movement_data = {
            "type": "orthogonal",
            "pattern": default_pattern,
            "piecePosition": [3, 3]
        }
        self.current_custom_pattern = default_pattern

        if hasattr(self, 'movement_pattern_buttons'):
            # Clear all button selections first
            for btn, _ in self.movement_pattern_buttons:
                btn.setChecked(False)
            # Select orthogonal button
            for btn, movement_type in self.movement_pattern_buttons:
                if movement_type == "orthogonal":
                    btn.setChecked(True)
                    break

        # Legacy compatibility
        if hasattr(self, 'move_combo'):
            self.move_combo.setCurrentText("Orthogonal")  # Default movement

        # Reset recharge
        if hasattr(self, 'recharge_combo'):
            self.recharge_combo.setCurrentText("turnRecharge")  # Default recharge

        # Reset internal data
        self.abilities = []
        self.primary_promotions = []
        self.secondary_promotions = []
        self.adjacency_recharge_pieces = []

        # Clear abilities selector (if it exists)
        if hasattr(self, 'ability_selector'):
            self.ability_selector.set_abilities([])

        # Clear adjacency recharge configuration
        if hasattr(self, 'adjacency_recharge_config'):
            self.adjacency_recharge_config = {
                'pieces': [],
                'distance': 1,
                'pattern': []
            }
            self.update_adjacency_recharge_preview_text()

        # Update UI after reset
        self.update_movement_controls()
        self.update_recharge_options()
        self.update_icon_previews()
        self.update_movement_pattern_preview()
        self.on_role_changed()

        # Ensure ability selector is cleared
        if hasattr(self, 'ability_selector'):
            self.ability_selector.set_abilities([])

        print("✓ Piece editor form reset to defaults")

    def post_load_update(self):
        """Update UI components after loading data (BaseEditor abstract method)"""
        # Update UI after data is loaded
        self.update_movement_controls()
        self.update_recharge_options()
        self.update_icon_previews()
        self.on_role_changed()
        self.status_widget.show_success("Piece loaded successfully")

    def post_save_update(self):
        """Update UI components after saving data (BaseEditor abstract method)"""
        # Refresh piece list dropdown
        self.refresh_load_piece_dropdown()
        self.status_widget.show_success("Piece saved successfully")

    def save_as_data(self) -> bool:
        """Save data with new filename (BaseEditor abstract method)"""
        return self.save_as_piece()

    def refresh_file_lists(self):
        """Refresh file lists/dropdowns (BaseEditor abstract method)"""
        self.refresh_load_piece_dropdown()

    def refresh_all_data(self):
        """Refresh all data and UI components - for menu refresh function"""
        print("🔄 Refreshing all piece editor data...")

        # Refresh file lists
        self.refresh_file_lists()

        # Refresh abilities list
        self.refresh_abilities_list()

        # Update all UI components
        self.update_movement_controls()
        self.update_recharge_options()
        self.update_icon_previews()
        self.update_movement_pattern_preview()
        self.on_role_changed()

        # Clear any stale pattern data
        if not hasattr(self, 'current_filename') or not self.current_filename:
            self.reset_pattern_data()
            self.update_movement_pattern_preview()

        self.status_widget.show_success("All data refreshed")
        self.log_console.append("All piece editor data refreshed")
        print("✓ Piece editor data refresh completed")

    def get_piece_names(self):
        """Get list of available piece names (for ability editor integration)"""
        return simple_bridge.list_pieces()


def main():
    """Main function for testing the piece editor"""
    import sys
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    window = PieceEditorWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()